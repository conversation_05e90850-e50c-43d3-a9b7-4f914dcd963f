我们对 React Compiler 期待已久。

自从从它第一次在 React Conf 2021 亮相。到现在 React Conf 2024 正式开源，已经过去了三年之久。

> 以前叫 React Forget，现改名为 React Compiler

### 1、背景


要了解 React Compiler，这还需要从 React 的更新机制说起。**React 项目中的任何一个组件发生 state 状态的变更，React 更新机制都会从最顶层的根节点开始往下递归对比，通过双缓存机制判断出哪些节点发生了变化，然后更新节点**。这样的更新机制成本并不小，因为在判断过程中，如果 React 发现 `props、state、context` 任意一个不同，那么就认为该节点被更新了。因此，冗余的 `re-render` 在这个过程中会**大量发生**。

> **对比的成本非常小，但是 re-render 的成本较高**，当我们在短时间之内快速更改 state 时，程序大概率会存在性能问题。因此在以往的开发方式中，掌握性能优化的手段是高级 React 开发者的必备能力

一个组件节点在 React 中很难被判断为没有发生过更新。因为 props 的比较总是不同的。它的比较方式如下。

```js
{} === {} 
// false
```

因此，高级 React 开发者需要非常了解 React 的默认优化机制，让 props 的比较不发生，因为一旦发生，那么结果必定是 false。


> 事实上，对 React 默认优化机制了解的开发者非常少，我们在开发过程中也不会为了优化这个性能去重新调整组件的分布。更多的还是使用 memo 与 useMemo/useCallback 暴力缓存节点

在这样的背景之下，冗余的 `re-render` 在大量的项目中发生。这也是为什么 React 总是呗吐槽性能不好的主要原因。当然，大多数项目并没有频繁更新 state 的需求，因此这一点性能问题表现得并不是很明显。

如果我们要解决冗余 re-render 的问题，需要对 React 默认优化技能有非常深刻的理解，需要对 `memo、useCallback、useMemo` 有准确的理解。但是普通的 React 开发者很难理解他们，有的开发者虽然在项目中大量使用了，但是未必就达到了理想的效果。React Compiler 则是为了解决这个问题，它可以自动帮助我们记忆已经存在、并且没有发生更新的组件，从而解决组件冗余 `re-render` 的问题。

从使用结果的体验来看，React Compiler 被集成在代码自动编译中，因此只要我们在项目中引入成功，就不再需要关注它的存在。**我们的开发方式不会发生任何改变。** 它不会更改 React 现有的开发范式和更新方式，侵入性非常弱。这一点对于老项目来说，非常非常重要。



### 2、**检测**

并非所有的组件都能被优化。因此早在 React 18 的版本中，React 官方团队就提前发布了严格模式。在顶层根节点中，套一层 `StrictMode` 即可。

```tsx
<StrictMode>
  <BrowserRouter>
    <App />
  </BrowserRouter>
</StrictMode>
```

遵循严格模式的规范，我们的组件更容易符合 React Compiler 的优化规则。

我们可以使用如下方式首先检测代码库是否兼容。在项目根目录下执行如下指令。

```bash
npx react-compiler-healthcheck
```

> 该脚本主要用于检测
>
> 1、项目中有多少组件可以成功优化：**越多越好**
>
> 2、是否使用严格模式，使用了优化成功率更高
>
> 3、是否使用了与 Compiler 不兼容的三方库

例如，我的其中一个项目，检测结果如下

<div style={{color: 'rgb(23, 163, 74)'}} className='border p-4 rounded'>
  <div>Successfully compiled 38 out of 40 components. </div>
  <div>StrictMode usage found.</div>
  <div>Found no usage of incompatible libraries.</div>
</div>

每一项都基本上通过了，那我就可以放心的在项目中引入对应的插件开始体验了。


### 3、**在项目中引入**

官方文档中已经明确表示，由于 JavaScript 的灵活性，Compiler 无法捕获所有可能的意外行为，甚至编译之后还会出现错误。因此，目前而言，Compiler 依然可能会有他粗糙的一面。因此，我们可以通过配置，在具体的某一个小目录中运行 Compiler。

```javascript
const ReactCompilerConfig = {
  sources: (filename) => {
    return filename.indexOf('src/path/to/dir') !== -1;
  },
};
```

React Compiler 还支持对应的 eslint 插件。该插件可以独立运行。不用非得与 Compiler 一起运行。

可以使用如下指令安装该插件

```bash
npm i eslint-plugin-react-compiler
```

然后在 eslint 的配置中添加

```javascript
module.exports = {
  plugins: [
    'eslint-plugin-react-compiler',
  ],
  rules: {
    'react-compiler/react-compiler': 2,
  },
}
```

Compiler 目前结合 Babel 插件一起使用，因此，我们首先需要在项目中引入该插件

```bash
npm i babel-plugin-react-compiler
```

然后，在不同的项目中，有不同的配置。

**添加到 `Babel` 的配置中**，如下所示

```javascript
module.exports = function () {
  return {
    plugins: [
      ['babel-plugin-react-compiler', ReactCompilerConfig], // must run first!
      // ...
    ],
  };
};
```

> 注意，该插件应该在其他 Babel 插件之前运行

**在 vite 中使用**

首先，我们需要安装 `vite-plugin-react`，注意不用搞错了，群里有的同学使用了 `vite-plugin-react-swc` 结果搞了很久没配置成功。然后在 vite.config.js 中，添加如下配置

```javascript
export default defineConfig(() => {
  return {
    plugins: [
      react({
        babel: {
          plugins: [
            ["babel-plugin-react-compiler", ReactCompilerConfig],
          ],
        },
      }),
    ],
    // ...
  };
});
```

**在 Next.js 中使用**

创建 `babel.config.js` 并添加上面 Babel 同样的配置即可。

**在 Remix 中使用**

安装如下插件，并且添加对应的配置项目。

```bash
npm i vite-plugin-babel
```

```javascript
// vite.config.js
import babel from "vite-plugin-babel";

const ReactCompilerConfig = { /* ... */ };

export default defineConfig({
  plugins: [
    remix({ /* ... */}),
    babel({
      filter: /\.[jt]sx?$/,
      babelConfig: {
        presets: ["@babel/preset-typescript"], // if you use TypeScript
        plugins: [
          ["babel-plugin-react-compiler", ReactCompilerConfig],
        ],
      },
    }),
  ],
});
```

**在 Webpack 中使用**

我们可以单独为 Compiler 创建一个 Loader. 代码如下所示。

```javascript
const ReactCompilerConfig = { /* ... */ };
const BabelPluginReactCompiler = require('babel-plugin-react-compiler');

function reactCompilerLoader(sourceCode, sourceMap) {
  // ...
  const result = transformSync(sourceCode, {
    // ...
    plugins: [
      [BabelPluginReactCompiler, ReactCompilerConfig],
    ],
  // ...
  });

  if (result === null) {
    this.callback(
      Error(
        `Failed to transform "${options.filename}"`
      )
    );
    return;
  }

  this.callback(
    null,
    result.code,
    result.map === null ? undefined : result.map
  );
}

module.exports = reactCompilerLoader;
```

我们可以在 React 官方了解到更多关于 React Compiler 的介绍与注意事项。具体地址如下


[https://react.dev/learn/react-compiler](https://react.dev/learn/react-compiler)

引入成功之后，我们可以在开发者工具中的 `Sources` 面板的 `Page` 目录中查看编译之后的代码，长得跟右侧代码差不多就表示编译成功了。
