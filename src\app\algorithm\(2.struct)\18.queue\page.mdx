import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'
import Demo01 from './demo01/preview'


### 队列

队列指的是一种排队机制，先来先服务，先到先得。这在我们的日常生活中非常常见。

表现在算法中，**队列**是一种**先进先出、后进后出**的线性数据结构。

![](/images/algorithm/struct/18.queue/1.png)


学习队列的相关知识，我们首先需要掌握队列容器的特性，其中包括

+ 队列是一个线性容器，他的两端都是开口的，通常我们会从一端进入，从另一端出来
+ 元素从开口的一端进入叫做**入队**，元素从开口的一端出来叫做**出队**
+ 元素出入的规则是**先进先出、后进后出**
+ 一个队列包含两个重要的概念：队头和队尾，如下图所示
+ 队列结构是线性的、有序的，因此我们可以使用数组或链表来实现队列结构

![](/images/algorithm/struct/18.queue/2.png)


队列的基本操作包括：

+ 入队：将元素添加到队列的末尾
+ 出队：将队列的第一个元素删除
+ 查看队列的第一个元素：查看队列的第一个元素，但不删除
+ 查看队列的长度：查看队列中元素的个数
+ 判断队列是否为空：判断队列中是否存在元素

从语法上来说，JavaScript 的数组本身就已经支持了队列的操作方式，因此，在实际开发中，我们不需要额外实现队列结构，直接使用数组即可。这也是最常规的使用方式。也比较简单，这里不多赘述

我们这里主要学习使用链表来实现队列结构。

但是，在一些情况下，由于基于数组实现入队操作，需要从数组的头部新增元素，因此，**入队操作的成本偏高**，与实际情况不符合，所以，我们有时会优先选择使用链表来实现队列结构。

我们通常需要结合双指针的概念，来实现队列结构。

+ 队头指针：指向队头元素
+ 队尾指针：指向队尾元素

在连续的线性结构之上，无论是入队操作，还是出队操作，我们只需要移动指针即可。

> 扩展：我们在 JavaScript 核心进阶中，学习到的 V8 引擎垃圾回收机制的 GC 复制算法，就使用了双指针实现队列的概念。


### 基于链表实现队列

首先，我们需要定义一个链表节点。当然，为了让指向头尾的指针能够更方便的移动，我们这里需要定义一个双向链表节点。

```ts
class ListNode {
  val: any;
  next: any;
  prev: any;
  constructor(val: any) {
      this.val = val;
      this.next = null;
      this.prev = null;
  }
}
```

入队，就是在链表的头部新增一个节点。

```ts
push(num: any) {
  // 在尾节点后添加 num
  const node = new ListNode(num);
  // 如果队列为空，则令头、尾节点都指向该节点
  if (!this.head) {
    this.head = node;
    this.tail = node;
    // 如果队列不为空，则将该节点添加到链表的头部
  } else {
    node.next = this.tail;
    this.tail!.prev = node;
    this.tail = node;
  }
  this.length++;
}
```

出队，就是删除链表最前面的节点。指针的移动就是往回移动一个节点。

```ts
pop() {
  const num = this.peek();
  this.head = this.head!.prev;
  this.head!.next = null;
  this.length--;
  return num;
}
```

理清了这两个链表操作，其他就很简单了，完整代码如下：

<CodeTabs>
```ts !!tabs queue.ts -c
!from ./demo01/queue.ts
```
</CodeTabs>

### 练习题：最近的请求次数

<Link href="https://leetcode.cn/problems/number-of-recent-calls/">原题：最近的请求次数</Link>

题目描述：

写一个 `RecentCounter` 类来计算特定时间范围内最近的请求。

请实现 `RecentCounter` 类：
+ `RecentCounter()` 初始化计数器，请求数为 0
+ `ping(int t)` 在时间 t 添加一个新请求，并返回过去 3000 毫秒内发生的所有请求数（包括新请求）。确切地说，返回在 `[t-3000, t]` 内发生的请求数。

保证每次对 `ping` 的调用都使用比之前更大的 t 值。


<Link href="/algorithm/20250605">点击查看解题思路</Link>







