import {r0} from './(0.preface)/router'
import {r1} from './(1.base)/router'
import {r2} from './(2.struct)/router'
import {r3} from './(3.binarysearch)/router'
import {rx} from './(x.daily)/router'


interface RouteItem {
  type?: string,
  name?: string,
  component?: any,
  path?: string,
  label?: string
}

export const routers: RouteItem[] = [
  ...r0,
  ...r1,
  ...r2,
  ...r3,
  ...rx,
].map(item => {
  if (item.path) {
    item.path = `/algorithm/${item.path}`
  }
  return item
})