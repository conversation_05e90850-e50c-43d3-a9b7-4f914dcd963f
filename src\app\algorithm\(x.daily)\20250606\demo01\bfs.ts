/**
 * Definition for a binary tree node.
 * class TreeNode {
 *     val: number
 *     left: TreeNode | null
 *     right: TreeNode | null
 *     constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
 *         this.val = (val===undefined ? 0 : val)
 *         this.left = (left===undefined ? null : left)
 *         this.right = (right===undefined ? null : right)
 *     }
 * }
 */

function levelOrder(root: TreeNode | null): number[][] {
  if (!root) return [];

  // 定义结果数组
  const result: number[][] = [];

  // 定义队列数组
  const queue: TreeNode[] = [root];

  // 遍历队列，遍历的结束条件为队列数组被清空
  while (queue.length > 0) {
    // 遍历时首先让节点直接出队，此时是利用了巧妙的方式避免了重复判断，
    // 所以最终的执行上与队列的出队入队时机有细微差异，但执行顺序是相同的。不得不利用这个方式是由于我们没有区分左节点右节点是否已经入队
    const levelSize = queue.length;
    // 定义当前层级数组
    const currentLevel: number[] = [];
    // 遍历当前层级，遍历的结束条件为当前层级数组被清空
    for (let i = 0; i < levelSize; i++) {
      // 出队
      const node = queue.shift()!;
      // 记录/访问节点值
      currentLevel.push(node.val);
      // 如果左节点存在，则将左节点入队
      if (node.left) queue.push(node.left);
      // 如果右节点存在，则将右节点入队
      if (node.right) queue.push(node.right);
    }
    // 将当前层级数组加入结果数组
    result.push(currentLevel);      
  }
  
  return result;
};