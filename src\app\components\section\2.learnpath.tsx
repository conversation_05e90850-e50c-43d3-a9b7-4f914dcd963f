'use client'

import { motion } from 'motion/react'
import { Sparkles, PhoneCall, GitBranch, LampFloor, GitBranchPlus, GitMerge, GitPullRequest, GitPullRequestDraft, GitPullRequestClosed, GitCommitVertical, Zap } from 'lucide-react'
import { variants } from './variants'
import Button from '@/components/ui/button'
import { useRouter } from 'next/navigation'

export default function SectionLearnPath() {
  return (
    <section className="py-4">
      <motion.div id='learnpath' {...variants(0.1)} className="mx-4 md:mx-auto max-w-6xl py-4 md:py-8 px-4 md:px-8 border-2 border-gray-200 dark:border-gray-700 rounded-lg border-dashed">
        <header className='flex items-center justify-between pb-2 md:pb-8'>
          <div className='flex items-center space-x-2'>
            <Sparkles className='w-6 h-6' />
            <h3 className='text-sm md:text-lg font-bold'>学习方向</h3>
          </div>
          <Button signal className='flex items-center space-x-2' onClick={() => {
            window.open('https://www.yuque.com/coreadvance/ar2my1/apk8cr', '_blank')
          }}>
            <span>量身定做，进一步咨询如何学习</span>
            <PhoneCall className='w-4 h-4' />
          </Button>
        </header>
        <div className='flex flex-wrap gap-2 md:gap-4'>
          <LearnPathItem icon={GitBranch} title='JS 核心' path='/learn/js-core' />
          <LearnPathItem icon={GitPullRequestDraft} title='超级 CSS' path='/supercss' />
          <LearnPathItem icon={GitPullRequest} title='React 基础' path='/reactzm' />
          <LearnPathItem icon={GitMerge} title='React 19' path='/r19base' />
          <LearnPathItem icon={GitBranchPlus} title='React 架构' path='/r19plus' />
          <LearnPathItem icon={GitPullRequestClosed} title='React 源码' path='/reactprinciple' />
          <LearnPathItem icon={GitCommitVertical} title='图解算法' path='/algorithm' />
          <LearnPathItem icon={LampFloor} title='Next.js' path='/nextjs' />
          <LearnPathItem icon={Zap} title='zustand' path='/zustand' />
        </div>
      </motion.div>
    </section>
    
  )
}

function LearnPathItem(props: { icon: any, title: string, path: string }) {
  const router = useRouter()
  return (
    <div onClick={() => router.push(props.path)} className='inline-flex items-center space-y-2 flex-col hover:bg-gray-100 dark:hover:bg-gray-700 p-4 rounded-sm cursor-pointer'>
      <props.icon className='w-6 h-6' />
      <div>{props.title}</div>
    </div>
  )
}