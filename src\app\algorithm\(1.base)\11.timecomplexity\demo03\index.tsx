import { useEffect, useRef } from 'react'
import functionPlot from 'function-plot'

export default function Demo02() {
  const input = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const bounds = input.current?.getBoundingClientRect()
    functionPlot({
      width: bounds?.width,
      target: '#timecomplexity_03',
      yAxis: {
        label: '时间复杂度',
        domain: [0, 10],
      },
      xAxis: {
        label: '输入规模',
        domain: [0, 5],
      },
      data: [
        {
          fn: '2^x',
        },
      ],
    })
  }, [])

  return (
    <div id='timecomplexity_03' ref={input} className='w-full' />
  )
}