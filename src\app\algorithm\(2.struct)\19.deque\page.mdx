import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'
import Demo01 from './demo01/preview'

### 双端对嘞

双端队列指的是一种允许从队列两端进行插入或删除操作的队列。

![](/images/algorithm/struct/19.deque/1.png)

双端队列的基本操作包括：

+ `head_push`：从队头入队，将元素添加到队列的头部
+ `tail_push`：从队尾入队，将元素添加到队列的尾部
+ `head_pop`：从队头出队，将队列的第一个元素删除
+ `tail_pop`：从队尾出队，将队列的最后一个元素删除
+ `head_peek`：查看队头元素，但不删除
+ `tail_peek`：查看队尾元素，但不删除

从语法上来说，JavaScript 的数组本身就已经支持了双端队列的操作方式，因此，在实际开发中，我们不需要额外实现双端队列结构，直接使用数组即可。这也是最常规的的使用方式。

但是，在一些情况下，由于基于数组实现入队操作，需要从数组的头部新增元素，因此，**入队操作的成本偏高**，所以，我们有时会优先选择使用链表来实现双端队列结构。

本篇文章，除了介绍双端队列的基本概念之外，另外一个目的，就是跟大家分享如何使用链表来实现**双端队列结构**。

### 基于链表实现双端队列

首先，我们需要定义一个链表节点。当然，为了让指向头尾的指针能够更方便的移动，我们这里需要定义一个双向链表节点。

```ts
class ListNode {
  val: any;
  next: any;
  prev: any;
  constructor(val: any) {
      this.val = val;
      this.next = null;
      this.prev = null;
  }
}
```

其次，按照上一章的逻辑，分别在两端实现入队与出队即可，完整代码如下

<CodeTabs>
```ts !!tabs queue.ts -c
!from ./demo01/queue.ts
```
</CodeTabs>

> 关于头与尾的概念是一个相对概念，因此在不同的场景下的指向可能有所不同，因此请不要混在一起去思考，最好是根据图示中的指针来理解