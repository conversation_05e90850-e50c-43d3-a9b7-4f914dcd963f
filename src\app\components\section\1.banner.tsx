'use client'

import { useRef } from 'react'
import { motion } from 'motion/react'
import { variants } from './variants'

export default function Banner() {
  const containerRef = useRef<any>(null)

  return (
    <motion.div id='banner' ref={containerRef} className='relative overflow-hidden -mt-16'>
      <div className=''>
        <div className='pt-24 pb-16 max-w-[1080px] mx-auto px-6 md:px-8 md:pt-32 md:pb-24'>
          <motion.h2 {...variants()} className='text-3xl md:text-5xl text-center font-bold mt-6'>
            <span className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-transparent bg-clip-text">
              前端学习，就该简单并深刻
            </span>
          </motion.h2>

          <motion.p {...variants(0.1)} className='text-center md:text-lg text-gray-600 dark:text-gray-300 mt-4 md:mt-6 mx-auto'>
            这波能反杀独立开发的前端技术教学类网站，为广大前端开发者提供优质的前端学习服务，让前端学习变得简单
          </motion.p>
        </div>
      </div>
    </motion.div>
  )
}
