import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'
import Demo01 from './demo01/preview'

### 双栈

**双栈**是使用两个栈结构来解决实践问题的一种比较巧妙的思路。除了在实践中有大量运用之外，在算法中，双栈思维也经常被使用。这篇文章则通过几个算法的案例，来介绍双栈思维的运用

### 案例：最小栈


**题目描述**：设计一个最小栈，支持 `push` ，`pop` ，`top` 操作，并能在常数时间内检索到最小元素的栈。

实现 `MinStack` 类:

- `MinStack()` 初始化堆栈对象。
- `void push(int val)` 将元素 `val` 推入堆栈。
- `void pop()` 删除堆栈顶部的元素。
- `int top()` 获取堆栈顶部的元素。
- `int getMin()` 获取堆栈中的最小元素。

原题地址如下

<Link href="https://leetcode.cn/problems/min-stack/">LeetCode 155. 最小栈</Link>

**解题思路**：

栈结构中，支持 `push` ，`pop` ，`top` 操作比较简单，经过上一章的学习，我们可以很容易就能做到。这里的难点是，如何在 $O(1)$ 时间复杂度之下，得到栈中元素的最小值。

通常情况下，这种需求，我们可以使用一个变量记录入栈的最小值是多少：每次入栈时，和当前最小值进行比较

```ts
// 伪代码
const min = null

const push = (val) => {
  if (min === null || val < min) {
    min = val
  }
}
```

但是这里比较麻烦的就是，栈还会有出栈操作，当出栈之后，新的最小值需要重新计算，又需要遍历栈中所有元素，时间复杂度为 $O(n)$，达不到常数时间的要求。因此，**我们需要使用一个辅助栈，来记录每次入栈的最小值**。

在每次入栈和出栈操作时，都需要确保辅助栈的栈顶元素，是当前栈中的最小值。

那么，我们就可以在每次入栈时，将当前元素和辅助栈的栈顶元素进行比较，如果当前元素小于辅助栈的栈顶元素，则将当前元素入栈到辅助栈中。

如果当前元素大于辅助栈的栈顶元素，则将辅助栈的栈顶元素再次入栈到辅助栈中。

这样，辅助栈的栈顶元素，就会始终是当前栈中的最小值。

然后出栈时，共同出栈即可。

完整代码如下所示

<CodeTabs>
```ts !!tabs Stack.ts -c
!from ./demo01/stack.ts
```
</CodeTabs>


需要注意，实现最小栈，并不是非要依赖于双栈来实现。但是我们这里的题目中，单独要求了在 $O(1)$ 时间复杂度之下，得到栈中元素的最小值。因此这是最适合的方式。


### 练习题：颜色值转换

<Link href="https://leetcode.cn/problems/sort-of-stacks-lcci/description/">原题：栈排序</Link>

题目描述：

编写程序，对栈进行排序使最小元素位于栈顶。最多只能使用一个其他的临时栈存放数据，但不得将元素复制到别的数据结构（如数组）中。该栈支持如下操作：

+ `push`
+ `pop`
+ `peek`  当栈为空时，`peek()` 返回 -1。
+ `isEmpty` 当栈为空时，返回 true


<Link href="/algorithm/20250604">点击查看解题思路</Link>


