在算法中，值的移动与指针的移动是两个高频概念。

在理解他们之前，我们首先要明确，在程序的运行过程中，内存空间本身其实是固定不变的。我们会在内存中存储值，内存的变化，往往指的是值的存储位置的变化。

### 1、值的移动

值的移动指的是，将一个值，从一个位置移动到另一个位置。表示**值本身在发生变化**，例如下面的例子，我们将值 10 从变量 a 移动到了变量 b。

```ts
let a = 10;
let b = a;
a = null
```

用图例表示如下：

![](/images/algorithm/base/4.move/1.jpg)

**数组**是元素移动比较常见的案例。由于**数组需要保证元素内存地址的连续性**，因此，数组中的许多操作，都需要进行元素的移动。例如，在数组中插入一个元素，就需要将插入位置之后的元素，都向后移动一个位置。

![](/images/algorithm/base/4.move/2.jpg)

![](/images/algorithm/base/4.move/3.jpg)

![](/images/algorithm/base/4.move/4.jpg)

![](/images/algorithm/base/4.move/5.jpg)

我们可以感受到，通常情况下，**元素移动的成本比较高**。这个结论，在后续的算法对比中，我们会经常用到。

### 2、指针的移动

虽然在 JavaScript 中，我们几乎不提指针的概念，但是由于算法是一个编程语言通用的概念，因此，在算法中，会频繁的提到指针。由于指针中，存储的是值的内存地址，因此，**指针的移动，变量指向的位置发生变化，而不是值本身的变化。**

例如，当我们使用 for 循环遍历数组时，指针会从数组的第一个元素，依次移动到数组的最后一个元素。但是在这个循环过程中，只要我们不做额外操作，数组不会因为指针的移动发生任何变化

```ts
let arr = [6, 3, 7, 4, 2];
for (let i = 0; i < arr.length; i++) {
  console.log(arr[i]);
}
```

![](/images/algorithm/base/4.move/6.jpg)

可以看到，指针的移动，由于不会造成数据本身进行频繁的变动，因此，**指针的移动是一个非常高效的操作**。

指针的移动往往用于队列、栈等数据结构中，在后续的章节中，我们会详细介绍这些数据结构，以及他们的实现方式。