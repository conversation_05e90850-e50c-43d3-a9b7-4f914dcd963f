import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 合并K个升序链表


<Link href="https://leetcode.cn/problems/merge-k-sorted-lists/">原题：合并K个升序链表</Link>

题目描述：

给你一个链表数组，每个链表都已经按升序排列。

请你将所有链表合并到一个升序链表中，返回合并后的链表。

```ts
// 输入
lists = [[1,4,5],[1,3,4],[2,6]]

// 输出
[1,1,2,3,4,4,5,6]
```


```ts
// 输入
lists = []

// 输出
[]
```

```ts
// 输入
lists = [[]]

// 输出
[]
```

### 解题思路

这个题目实际上非常简单。我们只需要先将数组打平，然后排序，最后再生成链表即可。

```ts
var mergeKLists = function (lists) {
  const list = [];
  // 将数组打平
  for (let i = 0; i < lists.length; i++) {
    let node = lists[i];
    while (node) {
      list.push(node.val);
      node = node.next;
    }
  }
  // 排序
  list.sort((a, b) => a - b);
  // 生成链表
  const res = new ListNode();
  let now = res;
  for (let i = 0; i < list.length; i++) {
    now.next = new ListNode(list[i]);
    now = now.next;
  }
  return res.next;
};
```

这个题之所以是困难级别的难度，是因为它需要我们使用优先级队列来解决这个问题。也就是小顶堆。所以，我们把之前学的优先级队列的堆排序逻辑拿过来替换排序即可。