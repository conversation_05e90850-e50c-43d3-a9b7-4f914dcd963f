
import { useState, useEffect } from 'react';

export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      // 检测触摸支持
      const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      // 检测屏幕尺寸
      const isSmallScreen = window.innerWidth < 768;
      
      // 移动端判断：支持触摸或屏幕小（更宽松的检测）
      setIsMobile(hasTouch || isSmallScreen);
    };

    // 初始检测
    checkMobile();

    // 监听窗口大小变化
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return isMobile;
}

