import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'
import Demo03 from './demo03/preview'

<Demo03 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo03/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo03/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo03/api.ts
```
</CodeTabs>

在上一章的基础之上，我们来做一个小的需求变动。

上一章的案例要求我们不要初始化时请求一条数据，因此，默认渲染结果是 `no data`

这一章的案例则不同，我们需要**在初始化时**请求数据。也就是说，我们此时同时需要初始化和更新的逻辑


### 1、**需求变动之后的思考**

在以前版本的实现中，由于接口数据的触发方式不同，因此我们需要分别处理这两种触发时机。

首先，我们需要定义两个状态来管理数据结果和 loading 状态

```ts
const [content, update] = useState({value: ''})
const [loading, setLoading] = useState(true)
```

然后，我们利用 `useEffect` 来实现初始化时的数据请求逻辑

```ts
useEffect(() => {
  getMessage().then(res => {
    update(res)
    setLoading(false)
  })
}, []);
```

按钮点击事件触发时，我们通过回调函数来实现数据的更新

```ts
function __handler() {
  setLoading(true)
  getMessage().then(res => {
    update(res)
    setLoading(false)
  })
}
```

完整代码如下

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo01/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>


### 2、**新的实现方式**

与之前版本的实现方式相比，新的开发方式就简单了许多。

我们只需要在上一章的案例中，做一个小的修改即可满足需求。那就是在给状态 promise 的初始值做一个小小的变动即可。

```ts
// !diff -
const [promise, update] = useState()
// 只需要改这一点代码
// !diff +
const [promise, update] = useState(getMessage())
```

这里的思路就是给状态 promise 赋值一个 Promise 对象作为初始值。修改之后的完整代码如下

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo02/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo02/api.ts
```
</CodeTabs>

**非常的方便省事。**


### 3、**完善细节**

这里有一个非常重要的小的细节，如果不考虑 Compiler 编译之后的代码去缓存初始化时的 `getMessage()`，那么每次更新组件时，该方法都会执行一次，因此，会导致冗余的接口请求。

> 使用 Compiler 编译之后，这段代码会被缓存下来而不会重复执行

我们期望的是 useState 中的初始化方法仅在组件首次渲染时执行一次，因此，最好的方式是进一步调整一下，利用 useState 的初始化机制修改如下

```ts
// !diff -
const [promise, update] = useState(getMessage())
// !diff +
const [promise, update] = useState(getMessage)
```

这样，即使不用 Compiler 编译缓存，也不会出现冗余请求的情况。结果如下


<Demo03 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo03/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo03/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo03/api.ts
```
</CodeTabs>