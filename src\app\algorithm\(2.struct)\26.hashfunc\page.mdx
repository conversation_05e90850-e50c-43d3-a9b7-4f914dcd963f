import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'
import Demo01 from './demo01/preview'



### 哈希函数与加密

> 本章仅做了解，不涉及具体实现

我们来看一下之前写过的一个比较简单的哈希函数

```ts
function hash(key: number) {
  return (key - 1000) % 8;
}
```

他有两个特性

+ 1、**确定性**：固定的输入，总能得到固定的输出
+ 2、**单向性**：我们**无法通过最终的输出值，反推出确定的输入值是多少**

因此，哈希函数常常会被运用到加密场景。

在实际中，我们通常会用一些标准哈希算法，例如 MD5、SHA-1、SHA-2 和 SHA-3 等。它们可以将任意长度的输入数据映射到恒定长度的哈希值。

近一个世纪以来，哈希算法处在不断升级与优化的过程中。一部分研究人员努力提升哈希算法的性能，另一部分研究人员和黑客则致力于寻找哈希算法的安全性问题。


| 哈希算法 | 输出长度 | 安全性 | 性能 | 是否被破解 | 冲突概率 | 
| -------- | -------- | ------- | ---- | ---- | ---- |
| MD5      | 128 bit   | 低      | 高   | 是 | 低 |
| SHA-1    | 160 bit   | 中      | 中   | 是 | 低 |
| SHA-2    | 256 bit   | 高      | 低   | 否 | 极低 |
| SHA-3    | 256 bit   | 高      | 低   | 否 | 极低 |


### 数据完整性验证

我们在做大文件传输时，常常需要对文件进行切片，然后分片传输。

在传输过程中，我们常常需要对文件进行完整性验证，以确保文件在传输过程中没有被篡改。能够在组合时百分百还原原始文件。

我们通常会利用哈希函数来对文件进行完整性验证。


### 数字签名

在实际中，我们常常会利用哈希函数来对文件进行数字签名。

iOS 的 App 签名机制是一个典型的数字签名应用场景。当开发者开发完一个 App 后，需要对其进行签名才能发布到 App Store 或进行分发。签名过程如下：

1. 首先，开发者需要向 Apple 申请一个数字证书，这个证书包含了开发者的公钥和私钥
2. 然后，系统会对 App 的二进制文件进行哈希计算，生成一个哈希值
3. 接着，使用开发者的私钥对这个哈希值进行加密，生成数字签名
4. 最后，将数字签名和证书一起打包到 App 中

当用户安装 App 时，系统会：
1. 使用证书中的公钥解密数字签名，得到原始哈希值
2. 对 App 的二进制文件重新计算哈希值
3. 比对两个哈希值是否一致，如果不一致，说明 App 被篡改，安装会被拒绝

这种机制确保了 App 的完整性和来源的真实性，防止了恶意软件的传播。

### 前端开发中对文件名的哈希处理

在实际中，我们常常会利用哈希函数来对文件名进行处理。我们会对文件内容计算哈希值（如MD5、SHA-1的子串），根据哈希函数的特性，内容变化时，算出来的哈希值也会发生改变。

此时，我们就可以利用哈希值来判断文件是否被改变。从而可以轻松做出下一步的打包优化判断。除此之外，给文件名添加哈希值，还有如下好处

+ **避免浏览器缓存问题**，浏览器会缓存静态资源（如JS、CSS），如果文件名不变，用户可能访问到旧版本的文件，导致功能异常。哈希文件名能确保每次内容变更后生成新的文件名，强制浏览器加载最新资源。

+ **增量更新优化性能**，只有被修改的文件会生成新哈希，未变化的文件哈希保持不变，用户只需下载更新的文件，减少网络开销。

+ **CDN和HTTP缓存友好**，可以设置长期缓存（如 Cache-Control: max-age=31536000），因为哈希文件名唯一对应内容，无需担心缓存过期问题。

+ **避免命名冲突**，在微前端或多团队协作中，哈希文件名能避免全局命名污染（如多个项目都有 main.js）。




