import { useEffect, useRef } from 'react'
import HashMap from './hashMap'

export default function Demo01() {
  const input = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // 使用示例
    const map = new HashMap();
    console.log('xxx hashmap')

    const s1 = {
      id: 1001,
      name: '张三',
      grade: 100
    }
    const s2 = {
      id: 1002,
      name: '李四',
      grade: 100
    }
    const s3 = {
      id: 1003,
      name: '王五',
      grade: 100
    }
    const s33 = {
      id: 1033,
      name: '张三',
      grade: 100
    }
    map.set(1001, s1)
    map.set(1033, s33)
    map.set(1002, s2)
    map.set(1003, s3)

    console.log(map)

    console.log(map.values())
  }, [])

  return (
    <div id='timecomplexity_01' ref={input} className='w-full' />
  )
}