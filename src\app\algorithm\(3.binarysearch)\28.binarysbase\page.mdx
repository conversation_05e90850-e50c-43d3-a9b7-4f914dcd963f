import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'

### 二分搜索基础

二分搜索又称二分查找，是一种在**有序集合**中查找目标值的算法。

一个基础题目如下：

<Link href="https://leetcode.cn/problems/binary-search/">原题地址：704. 二分查找</Link>

> 给定一个 `n` 个元素有序的（升序）整型数组 `nums` 和一个目标值 `target`  ，写一个函数搜索 `nums` 中的 `target`，如果 `target` 存在返回下标，否则返回 `-1`。

我们可以利用二分搜索的思路，来解决这个问题。

首先，我们定义两个指针，分别指向数组的起始位置和末尾位置。

![](/images/algorithm/binarysearch/2.png)

由于数组是一个升序，因此，我们可以通过起始位置获得处于中间位置的元素下标

![](/images/algorithm/binarysearch/3.png)

然后我们可以通过比较中间元素与目标值的大小，来确定目标值在左半部分还是右半部分。确定好之后，在根据实际情况移动左右指针，此时我们的目标值是 3，在中间元素的左侧，此时，改变右指针，使其指向中间元素

![](/images/algorithm/binarysearch/4.png)


此时，左右节点构建了新的区间，但是目标元素还是没找到，因此，我们要在这个新的区间里，重新计算中间元素的位置，然后继续比较中间元素与目标值的大小，来确定目标值在左半部分还是右半部分。

重复上面的动作，直到找到目标元素。

此时我们会发现，左右指针会在查找的过程中不断靠近，因此，我们只需要判断左右指针是否相遇，如果相遇，则说明目标元素不存在，返回 -1。

代码的实现如下：

```ts
function search(nums: number[], target: number): number {
  let left = 0;
  let right = nums.length - 1;
  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    if (nums[mid] === target) {
      return mid;
    } else if (nums[mid] < target) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }
  return -1;
}
```


这里需要注意的细节是，我们在计算中间元素位置时，并不需要总是算出最中间的元素。例如，当区间总体个数为单数 ,我们可以刚好找到一个最中间的元素。但是，当区间总体个数为双数时，我们找到的元素就会要么偏左，要么偏右。这并不影响最终的结果。







