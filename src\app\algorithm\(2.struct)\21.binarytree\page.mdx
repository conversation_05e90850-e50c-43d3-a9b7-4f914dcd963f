import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'
import Demo01 from './demo01/preview'

### 二叉树

对于前端开发者来说，树状结构非常常见。DOM 节点、组件树、文件系统目录等，都是树状结构。在算法的学习中，我们主要关注**二叉树：一种每个节点最多有两个子节点的树形数据结构**。

二叉树的两个节点分别称为左子节点和右子节点。

二叉树的每个节点都包含一个值，以及指向其左子节点和右子节点的指针。

```ts
class TreeNode {
  val: number;  // 节点值
  left: TreeNode | null; // 左子节点指针
  right: TreeNode | null; // 右子节点指针

  constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
    this.val = val === undefined ? 0 : val;
    this.left = left === undefined ? null : left; 
    this.right = right === undefined ? null : right; 
  }
}
```

二叉树的基本概念包括

+ 根节点（`Root`）：树的顶端节点，没有父节点
+ 叶子节点（`Leaf`）：没有子节点的节点
+ 层（`Level`）：从根节点到当前节点的层数，根节点为第 1 层，根节点的子节点为第 2 层，以此类推
+ 深度（`Depth`）：与层数相同，根节点深度为 1，根节点的子节点深度为 2，以此类推
+ 高度（`Height`）：从当前节点到最远叶子节点的层数
+ 子树（`Subtree`）：以某个节点为根的树

![](/images/algorithm/struct/21.binarytree/1.png)


### 二叉树的基本操作

我们可以很明显的感受到，二叉树的基本操作与链表基本一致。都是先创建节点，然后通过指针来连接节点。

**1、初始化二叉树**

二叉树的节点包含一个值，两个指针，因此我们定义一个节点时需要兼顾到这个三个属性

```ts
class TreeNode {
  val: number;
  left: TreeNode | null;
  right: TreeNode | null;

  constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
    this.val = val === undefined ? 0 : val;
    this.left = left === undefined ? null : left;
    this.right = right === undefined ? null : right;
  }
}
```

有了这个类之后，我们就可以创建二叉树的节点了

```ts
const root = new TreeNode(1);
const left = new TreeNode(2);
const right = new TreeNode(3);
```

然后通过指针来连接节点，一颗简单的二叉树就创建好了

```ts
root.left = left;
root.right = right;
```

**2、插入节点**

如图所示，我们想要在刚才创建好的二叉树中插入一个节点。

![](/images/algorithm/struct/21.binarytree/2.png)

很显然，我们会采用与链表操作类似的方式去插入节点，首先，创建新的节点

```ts
const node = new TreeNode(6);
```

然后，将根节点指向该节点，并让该节点指向根节点的左子节点

```ts
root.left = node;
node.left = left;
```

只需要操作对应的指针指向即可。

**3、删除节点**

如果我们想要把刚才插入的节点又删除掉，也只需要操作对应的指针指向即可。

```ts
// 重新指向根节点的左子节点
root.left = left;
```

> 由于树状结构中，删除操作比较灵活，但是需求如何，都是通过修改指针的方式来实现，对于代码上没有难度，因此这里就不再多做扩展，到时候我们再具体的题目中学习即可

### 二叉树的遍历

我们在前面迭代一章，已经学习过二叉树的遍历方式。这一章，我们换个角度，从深度优先遍历和广度优先遍历的角度再重新学习一下。

**1、深度优先遍历**

深度优先遍历（`Depth-First Search`，简称 DFS）是一种遍历二叉树的算法，它沿着树的深度遍历树的节点。在深度优先遍历中，我们优先访问当前节点的左子树，若这个左子节点还有左子节点，则继续访问，直到到达叶子节点，然后回溯访问右子节点，直到父节点，再访问右子树。

DFS 的遍历顺序与递归的逻辑是相同的。

因此，这也符合函数调用栈的执行顺序。那么，在实现时，我们除了可以利用递归来实现之外，也可以利用栈来实现这个遍历过程。

<Link href="/algorithm/(1.base)/10.iteration">详细的讲解大家可以回过头去参考迭代一章。</Link>

实现的完整代码如下所示：

<CodeTabs>
```ts !!tabs answer.ts -c
!from ./demo01/answer.ts
```
</CodeTabs>

**2、广度优先遍历**

广度优先遍历（`Breadth-First Search`，简称 BFS）是一种遍历二叉树的算法，它沿着树的宽度遍历树的节点。在广度优先遍历中，我们优先访问当前节点的所有子节点，然后依次访问这些子节点的子节点，直到到达叶子节点。

BFS 的遍历顺序与队列的执行顺序是相同的。

理解起来会有点绕，我们用继续用图士的方式结合起来理解。

首先，假如我们有如下二叉树


![](/images/algorithm/struct/21.binarytree/3.png)

为了实现广度优先遍历，我们需要在旁边定义一个队列，然后从根节点开始，将根节点入队。

```ts
const queue = [root];
```

![](/images/algorithm/struct/21.binarytree/4.png)

然后，我们开始遍历队列，首先将根节点出队，在节点出队时，访问节点，并将根节点的左右子节点入队。

```ts
const node = queue.pop()!;
console.log(node.val); // 访问节点
if (node.left) {
  queue.unshift(node.left)  // 左子节点入队
}
if (node.right) {
  queue.unshift(node.right) // 右子节点入队
}
```

此时，1 出队，2、3 入队

![](/images/algorithm/struct/21.binarytree/5.png)

然后，我们继续遍历队列，将队列中的节点出队，并访问节点，并将节点的左右子节点入队。

此时，2 出队，4、5 入队


![](/images/algorithm/struct/21.binarytree/6.png)



3 出队，6、7 入队

![](/images/algorithm/struct/21.binarytree/7.png)

4 出队，8、9 入队

![](/images/algorithm/struct/21.binarytree/8.png)

5 出队，10、11 入队

![](/images/algorithm/struct/21.binarytree/9.png)

6 出队，12、13 入队

![](/images/algorithm/struct/21.binarytree/10.png)

7 出队，14、15 入队

![](/images/algorithm/struct/21.binarytree/11.png)

8 出队，由于 8 已经没有子节点了，所以没有元素入队

![](/images/algorithm/struct/21.binarytree/12.png)

9 出队，由于 9 已经没有子节点了，所以没有元素入队

![](/images/algorithm/struct/21.binarytree/13.png)

后续的元素都是最后一层的叶子节点，没有子节点，所以没有元素入队。依次出队即可。

因此，最后我们的访问顺序为：1、2、3、4、5、6、7、8、9、10、11、12、13、14、15

这就是广度优先遍历的整个过程。

实现的完整代码如下所示：

<CodeTabs>
```ts !!tabs bfs.ts -c
!from ./demo01/bfs.ts
```
</CodeTabs>

### 二叉树类型

**1、完美二叉树**

如下图所示，完美二叉树（perfect binary tree）所有层的节点都被完全填满。在完美二叉树中，若树的高度为 h，则节点总数为 $2^h - 1$

![](/images/algorithm/struct/21.binarytree/3.png)

**2、完全二叉树**


如下图所示，完全二叉树（complete binary tree）除了最后一层外，每一层的节点都被完全填满，最后一层的节点从左到右依次填满。


![](/images/algorithm/struct/21.binarytree/14.png)


**3、满二叉树**

满二叉树（full binary tree）除了叶节点之外，其余所有节点都有两个子节点，被完全填充。

![](/images/algorithm/struct/21.binarytree/15.png)


**4、平衡二叉树**

如图所示，平衡二叉树（balanced binary tree）是指任意节点的左子树和右子树的高度之差的绝对值不超过 1 的二叉树。

![](/images/algorithm/struct/21.binarytree/15.png)


### 总结

不管是在算法中，还是在实践中，二叉树的运用都是非常多的。因此，二叉树是一个比较重要的知识点。在后续的章节中，我们会基于二叉树扩展更多的基础能力。

相关的练习题

<Link href="/algorithm/20250414">二叉树的最大深度</Link>

<Link href="/algorithm/20250415">平衡二叉树的判断</Link>

<Link href="/algorithm/20250416">二叉树的后序遍历</Link>

<Link href="/algorithm/20250606">二叉树的层序遍历</Link>