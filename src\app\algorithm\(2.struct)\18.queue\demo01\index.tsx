import { useEffect, useRef } from 'react'
import functionPlot from 'function-plot'

export default function Demo01() {
  const input = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const bounds = input.current?.getBoundingClientRect()
    functionPlot({
      width: bounds?.width,
      target: '#timecomplexity_01',
      yAxis: {
        label: '时间复杂度',
        domain: [0, 10],
      },
      xAxis: {
        label: '输入规模',
        domain: [0, 5],
      },
      data: [
        {
          fn: 'x',
        },
      ],
    })
  }, [])

  return (
    <div id='timecomplexity_01' ref={input} className='w-full' />
  )
}