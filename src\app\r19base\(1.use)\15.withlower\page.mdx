import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs use.ts -c
!from ./demo01/use.ts
```

```tsx !!tabs userinfo.tsx -c
!from ./demo01/userinfo.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>

`use + Suspense` 的结合，可以极大的简化我们的开发代码，在开发体验上的提升是非常明显的。但是，如果由于客观原因，我们的项目无法升级到 React 19，那么我们就无法享受这种开发体验带来的提升了吗？

当然不是。早在 React 16 的后期版本中，就提供了对 Suspense 的支持，因此，我们只需要在这个基础之上，自己定义一个 use 方法，去读取 promise 中的值就可以实现我们前面所有章节的效果了。

自定义的代码如下

```tsx use.ts -c
!from ./demo01/use.ts
```

如果你要理解这段代码，可以结合我们前面专门分析过的 Suspense 的运行原理来理解。

定义好之后，我们原本需要从 React 中引入的 use 方法，从这个模块里引入就可以了

```ts
// !diff -
import React, {use} from 'react'
// !diff +
import use from './use'
```