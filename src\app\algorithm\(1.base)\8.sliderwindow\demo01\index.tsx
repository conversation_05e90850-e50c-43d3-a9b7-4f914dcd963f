import { useRef } from 'react'

import { maxSum } from './max_a'

export default function Demo01() {

  const input = useRef([4, 2, 1, 7, 8, 1, 2, 8, 1, 0])
  const k = useRef(4)

  return (
    <div className=''>
      <div className='text-sm text-gray-600 my-2'>输入</div>
      <div className='bg-gray-100 dark:bg-gray-800 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'>[{input.current.join(', ')}]</div>
      </div>

      <div className='bg-gray-100 dark:bg-gray-800 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>k = </div>
        <div className='code font-bold'>{k.current}</div>
      </div>

      <div className='text-sm text-gray-600 my-2'>输出</div>
      <div className='bg-gray-100 dark:bg-gray-800 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'>{maxSum(input.current, k.current)}</div>
      </div>
    </div>
  )
}