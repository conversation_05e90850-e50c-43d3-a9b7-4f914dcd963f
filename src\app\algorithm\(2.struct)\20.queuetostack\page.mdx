import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'
import Demo01 from './demo01/preview'

### 基于队列实现栈

基于队列实现栈并不是一个常规的实现方式。也并非是一个日常工作中，需要用到的思路。

他主要出现的场景基本上就只会在算法面试中出现。

不过，这对于我们理解栈和队列有非常大的帮助。因此单独开一章做一个简单的介绍。


基于队列实现栈的难度并不是很高，我们上一章中，使用双向链表实现的双端队列，就具备栈的特性。


除此之外，我们这里主要基于两个单向队列来实现栈的特性

<Link href="https://leetcode.cn/problems/implement-stack-using-queues/description/">原题地址：队列实现栈</Link>

### 基于两个单向队列实现栈

由于队列是先进先出，而栈是先进后出，因此，我们可以基于两个队列来实现栈的特性。核心思想是，当有新的元素入队时，让第一个队列中已经存在的元素，出去并进入到第二个队列中。

确保新进入的元素为队首时，在将刚才进入到第二个队列中的元素重新进入到第一个队列中。

我们先使用图示来理解一下这个过程，首先，当第一个元素入栈时，内部直接进入到第一个队列

![](/images/algorithm/struct/20.queuetostack/1.png)

当第二元素入栈，我们需要先将第一个元素从队列中弹出，进入到辅助队列中去暂时先存起来，然后让第二个元素直接进入队列

![](/images/algorithm/struct/20.queuetostack/2.png)

然后，当第二个元素进入队列之后，我们还需要将辅助队列中的元素重新进入到第一个队列中，确保新进入的元素为队首

![](/images/algorithm/struct/20.queuetostack/3.png)

这样，第二个元素作为后入栈的元素，就成为了队首元素，就可以很容易做到先出栈了。**后进先出**

我们使用代码来实现这一逻辑


<CodeTabs>
```ts !!tabs queue.ts -c
!from ./demo01/queue.ts
```
</CodeTabs>











