**前端是不需要学习算法的。**

如果，你只是想当一名普通的前端开发的话。


相信大家都有真实的工作经历，前端在常规项目的开发的过程中，实际上几乎不会遇到需要很深的算法知识才能解决的问题。因此，许多前端开发人员都通常会觉得：学习算法是没有必要的。

因此，我也并不奢望能够说出一段话，就能够让大家直接就感受到算法的重要性。这个需要每个人根据自己的实际情况去感悟和判断，值得高兴的是，依然有许多前端开发认可学习算法是前端开发必备的基础能力。

普通的团队，更注重运用与实践，因此会以是否熟练掌握一门项目框架为标准，来评判一个前端开发人员的水平是否合格，例如 Vue、React、Angular 等等。

但，当我们想要在前端开发领域更进一步的时候，算法能力就会变得尤为重要。**好的团队，往往更重视开发者的这种基础编程能力**。因此通常会在面试的过程中，视情况而定去考察候选人的算法能力。帮助前端开发者，构建算法思维，是我设计这本专栏的核心目的。

### 免费的算法教程那么多，为什么要重新写

主要出于以下几个考虑

+ 1、许多算法教程并不直接基于 JavaScript 来编写，因此对于前端开发而言会增加更多的学习成本
+ 2、许多算法算法教程有一个非常明显的特点，就是同质化严重。对于基础薄弱的朋友来说，要凭借这些免费的算法资料达到一个合格的水平，是非常困难的。他们表达得比较简略、粗糙，学习难度较高
+ 3、有的资料对于基础内容的讲解不够完整和精准，容易产生误解
+ 4、不够深入。许多朋友学完之后的反馈就是没有**获得感**，进展缓慢并且容易忘记
+ 5、对与比较复杂的算法解答，没有清晰的推算步骤，学习过程中理解困难
+ 6、结合实践场景，进一步构建算法知识体系

因此基于这些痛点，我决定单独使用我自己的思维，从**深入浅出**的角度出发，从零开始编写一套算法学习教程，尝试去解决这些问题，希望能够帮助大家在学习算法的过程中，能够更加轻松、高效、愉快。

### 2、学习路径


我们会从 0 开始学习，逐步深入，最终会练习到一些比较复杂的算法和大厂真题，因此主要的学习路径如下所示

> 可能会在实践的过程中有所调整，但是整体的学习路径不会发生太大的变化

+ 基础的语法知识
+ 基础数据结构深入学习
+ 算法思想培养与练习
+ 算法复杂度分析
+ 搜索与排序
+ 分治策略
+ 动态规划
+ 贪心算法
+ 回溯算法
+ 树
+ 图
+ 字符串相关
+ 高级数据结构
+ 大厂真题练习
+ 算法思维拓展
+ 算法与实践场景体系构建
+ 算法面试准备：日常刷题

### 3、更新频率

请注意，本专栏并不会集中在某一个特定的时间段里密集更新。我会兼顾到大家的学习进度，最低确保三天一更。

每更新完一章，如果有同学反馈学习困难，针对这一章，我会就近选择时间在付费群内直播讲解。以便大家能够更加高效、愉快地学习