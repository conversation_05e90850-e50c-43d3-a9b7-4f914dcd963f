.index_tab_case {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  background-color: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.index_tab_case nav.nav {
  background-color: #FDFDFD;
  padding: 5px 5px 0;
  border-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: 1px solid #EEEEEE;
  height: 44px; 
}

.index_tab_case .reset {
  list-style: none;
  margin: 0;
  padding: 0;
  font-weight: 500;
  font-size: 14px;
}

.index_tab_case ul.tabs {
  display: flex;
  width: 100%;
  height: 39px;
}

.index_tab_case li.tab {
  border-radius: 5px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  width: 100%;
  padding: 0px 15px;
  position: relative;
  background-color: white;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-width: 0;
  user-select: none;
  color: #0f1115;
}

.index_tab_case .underline {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #0d63f8;
}

.index_tab_case main {
  display: flex;
  justify-content: center;
  margin-top: 60px;
  flex: 1;
}

.index_tab_case main .icon {
  font-size: 128px;
}