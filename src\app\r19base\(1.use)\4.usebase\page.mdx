import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'
import Demo03 from './demo03/preview'

`use()` 是 React19 提升异步开发体验最重要的 hook。也是让 useEffect 重要性大幅度削弱的主要原因。

我们可以利用 use 读取 `Promise` 中的值。

```ts -c
const value = use(promise)
// 读取到的 value 值是 promise 中 resolve 出来的值
```

> 也可以使用 use 读取 context 中的资源，后续详细介绍该能力


### *1*、正确理解 promise

这里我们需要特别注意的是，**Promise** 是指的一个已经创建好的 Promise 对象，并且，在该 promise 对象中已经有了确定的 `resolve` 的结果，use 读取的是 resolve 的值。

注意观察一下下面两种写法

第一种是已经有了结果状态的 Promise 对象

```ts -c
const _api2 = new Promise((resolve) => {
  resolve({ value: '_api2' })
})

// good
const result = use(_api2)
```

第二种是函数运行创建 Promise 对象，此时我们需要注意，虽然 _api3 执行之后会立即返回一个带有 resolve 结果状态的 Promise，但是 use 并不能第一时间读取到其值。

```ts -c
const _api3 = () => {
  return new Promise(resolve => {
    resolve({ value: '_api3' })
  })
}

// bad: get an error
const result = use(_api3())
```

如果我们直接使用第二种，那么运行之后，React19 会给你如下一个报错。

```error -error
async/await is not yet supported in Client Components, only Server Components. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.
```

一个完整的案例代码以及演示效果如下

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo01/message.tsx
```

</CodeTabs>



### *2*、在条件判断中使用

> 这是一个反模式，并不建议在实践中真的这样使用

和其他 hook 一样，`use()` 必须在函数组件中使用。但是很不一样的是，use 可以在循环和条件判断语句中使用。

```ts
if (!loading) {
  result = use(_api2)
}
```

完整的代码与最终的演示效果如下，你可以在演示案例中多次点击切换按钮查看交互效果。

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo02/message.tsx
```

</CodeTabs>


### *3*、在异步请求中使用

通常，我们在处理异步请求时，也会结合 promise 来使用。但是我们并不能直接使用 use 来读取异步请求中的 promise，因为我们已经非常明确，use 只能读取有确定 resolve 结果的 promise 中的值。但是有可能第一时间异步请求包装的 promise 状态为 pending。因此在这种情况下，我们必须结合 Suspense 来使用。

当然，为了加强对 use 的理解，我们也准备了一个不顾任何风险提示，强行等 promise 请求完成之后使用 use 去读取它的值的案例。代码与演示效果如下。

<Demo03 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo03/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo03/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo03/api.ts
```
</CodeTabs>

点击预览工具栏中的刷新按钮可以重新加载执行该组件。

我们可以观察一下效果，在目前的 React 版本中，并不能合理的处理好这种存在风险的读取方式，虽然我们最终读取到了 promise 中的值，内容也顺利渲染出来了，但是中间存在一次明显的闪烁。这种体验非常糟糕。

因此，在实践中，如果我们要读取异步请求的 promise 中的值，必须结合 Suspense 来处理。

接下来我们要学习 Suspense 的详细知识。

