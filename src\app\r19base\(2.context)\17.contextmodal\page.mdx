import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'

> 本章内容理解起来难度偏高，只需简单阅读，理解和感受 ref 的作用即可，**无需掌握**。


### 1、**基于 ref 封装一个 Madal 组件**

我们的目标是封装一个 Modal 组件，但是我并不需要传入 `show` 等状态来控制 Modal 的显示与隐藏。而是使用 ref 来控制。目标代码如下

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```
</CodeTabs>

这里的核心是，当我想要让弹窗显示时，只需要调用 `modal.current.show()` 即可。这种方式可以有效避免在应用层组件中声明过多的状态来控制一切。

对于刚接触 React 的新人来说，要实现这种带有动画的显示隐藏效果比较困难。我们这里就简单来拆解一下实现思路。

首先，基于数据驱动 UI 的思路，那么在弹窗组件内部，我们肯定要首先定义一个状态 `show`， 用于控制弹窗的显示与隐藏。

```js
function Modal() {
  const [show, setShow] = useState(false)
  ...
}
```

接下来估计部分道友就比较懵了，因为如果只是靠 show 的话，如何与动画结合起来考虑呢？很显然，隐藏的时候，我们最终要删除节点，但是节点删除之后，动画就直接消失了。因此在这个逻辑里，我们要分成两个步骤来处理动画的执行与节点的删除。

首先，在弹窗显示时，应该是先将 DOM 节点加入进来，然后再执行入场动画

在弹窗消失时，应该是先执行退场动画，然后再将 DOM 节点删除。

因此，这个时候，我们还需要引入另外一个状态用于分别控制动画的执行。

```javascript
function Modal() {
  // 控制动画的执行
  const [show, setShow] = useState(false)
  // 控制节点的增删
  const [display, setDisplay] = useState(false)
  ...
}
```

由于 show 我们约定是用来控制动画的执行，因此，在 css 上，我们根据 `show` 的值来确定在 class 中加入入场动画还是退场动画

```js
const cls = clsx('modal', {
  'animation-in': show,
  'animation-out': !show
})
```

然后在入场时，首先要新增节点，并且立即执行动画

```javascript
show: () => { setShow(true); setDisplay(true) }
```

在退场时，首先要执行动画，并在动画执行结束之后，再执行删除节点的操作

```javascript
close: () => { setShow(false) }

...

function __animationendHandler() {
  if (!show) setDisplay(false)
}
```

这样，把逻辑稍微再整理一下，就能够实现动画效果了。

当然，本次案例最核心的重点，还是使用 `useImperativeHandle` 把内部的操作提供给外部的控制器 ref

```js
useImperativeHandle(ref, () => ({
  show: () => { setShow(true); setDisplay(true) },
  close: () => { setShow(false) }
}))
```



### 2、**基于 Modal 封装 Dialog 组件**

先来点击按钮感受一下演示效果。

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs dialog.tsx -c
!from ../../../../components/ui/dialog/index.tsx
```
</CodeTabs>

这个案例中，比较有难度的是我们记录了鼠标的点击位置，并基于该位置对内部元素进行放大显示。退场时，还要往这个位置进行缩小隐藏。原理并不复杂，只是如果自己实现的话，要稍微花点时间调一下位置的计算。大家在使用时可以直接 copy 这段代码就行，我在兼容性上也做了完整的处理。

动画是基于缩放 scale 来实现，这里我们只需要基于鼠标点击的位置和目标位置更改变换中心即可，具体的计算细节请看右侧代码。动画 css 代码如下所示。

```css
.dialog.in {
  animation-name: dialog-show;
}
.dialog.out {
  animation-name: dialog-hide;
}

@keyframes dialog-show {
  0% {transform: scale(1); opacity: 0;}
  1% { transform: scale(0);}
  100% { transform: scale(1); opacity: 1; }
}
@keyframes dialog-hide {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(0); opacity: 0; }
}
```

这里我们需要观察的是，ref 的多层传递和使用。在我们使用 `Modal` 时，并不需要特别关注 ref 在内部是如何处理的，只需要知道如何使用它即可。我们可以利用这种思维把类似的嵌套逻辑简化成单一逻辑。
