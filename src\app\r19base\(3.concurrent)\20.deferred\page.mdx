import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'
import Counter01 from './counter01/preview'
import Counter02 from './counter02/preview'
import Counter03 from './counter03/preview'
import Counter04 from './counter04/preview'


### 1、效果对比

重头戏来了！

在进一步学习之前，道友们先操作感受一下这个案例中，搜索时 loading 的交互状态。在输入框中，快速输入任意内容。这是未使用并发 API 实现的交互效果。

<Demo01 />


然后接下来是使用了并发 API 的交互效果。

<Demo02 />

很显然，我更偏好于第二个案例的 Loading 交互方案。

他的具体表现为：初始化时，显示 Skeleton 组件，表示此时列表中没有内容，即将加载新的内容进来。

**更新时，保持现有列表**，可在此的基础之上显示 Loading/蒙层，如果你确定接口请求的时间非常短，也可以没有任何 Loading 显示

在前面的学习中，我们利用 Suspense 与 use 轻松做到了第一种交互方案，这种交互方案无论是在初始化还是在更新时，都是通过显示 Skeleton 来表示请求正在发生。接下来，我们需要学习如何利用新的知识，来做到第二种交互方案。


### 2、**useDeferredValue**

在 React 并发模式的基础之上，我们可以利用 useDeferredValue 在不变动代码顺序的情况之下，推迟指定 UI 更新任务的执行。

> 并发模式的基础理论详细学习，你需要关注我的 React 知命境

```js
const deferredValue = useDeferredValue(value, initialValue?)
```

对于刚接触的道友们来说，useDeferredValue 理解起来比较困难，我们循序渐进的用 4 个案例来理解一下。

首先，在正常情况下，一个 state 的变化，会导致 UI 发生变化。

例如下面这个案例，状态 `counter` 被两个元素使用，这两个元素的更改，实际上是**一个任务**，他们必定会同时响应 counter 的变化。

因此，当你快速点击按钮时，两个元素的变化是同时进行的。

<Counter01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./counter01/index.tsx
```
</CodeTabs>



这个时候，我们可以利用 useDeferredValue，把他们拆分成两个任务。

<Counter02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./counter02/index.tsx
```
</CodeTabs>


注意看，我们使用 counter 作为 useDeferredValue 的初始值

```ts
const deferred = useDeferredValue(counter)
```

并将其返回值替换第二个元素

```tsx
// return
<div className='mt-4'>counter: {deferred}</div>
```

此时，第二个元素的更新，就不再与第一个元素同步。它更新的优先级被降低。这个时候它的执行在理论上是可以被更高的优先级插队和中断的。

快速点击按钮查看一下演示效果，但是由于渲染都太短了，我们肉眼无法区分出来两个任务已经被分开了，因此，从视觉上的表现结果和刚才的案例没啥区别。


为了看出区别，我们把第二个元素重构成一个子组件，并模拟成一个耗时组件

<Counter03 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./counter03/index.tsx
```

```tsx !!tabs expensive.tsx -c
!from ./counter03/expensive.tsx
```
</CodeTabs>

快速点击按钮，此时我们发现，两个按钮的更新可以区分开了。并且在快速点击时，第二个 counter 的更新，会落后于第一个 counter，此时表示，我们可以利用 useDeferredValue 推迟 UI 的更新。将对应任务的优先级降低，使其可以被插队与中断。


此时我们也发现另外一个情况，那就是 counter 直接对应的高优先级执行也没有那么流畅，这是为什么呢？其实很简单，因为在我们的模拟案例中，**并没有把耗时定位在渲染上**。这就和实际的情况不太一样了。我们把耗时写在了 Expensive 函数里，而这个函数每次都会执行，它的执行阻塞了渲染。所以我们会觉得第一个 counter 的更新变得比较卡顿

> 所以这里我们一定要区分开渲染任务和 Expensive 函数，他们是不同的，UI 渲染是一个异步任务，而 Expensive 函数是同步执行的。useDeferredValue 推迟的是 UI 渲染任务。因此，我们需要特别注意的是，不要在同步逻辑上执行过多的耗时任务。

这里我们可以通过任务拆分的方式，把执行耗时时间分散到更多的子组件中去，这样 React 就可以利用任务中断的机制，在不阻塞渲染的情况下，中断低优先级的任务。

再来感受一下演示结果，我们就发现，第一个 counter 的更新也变得非常流畅了。

<Counter04 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./counter04/index.tsx
```

```tsx !!tabs expensive.tsx -c
!from ./counter04/expensive.tsx
```

```tsx !!tabs slow-item.tsx -c
!from ./counter04/slow-item.tsx
```
</CodeTabs>

此时我们注意观察，不要错漏这个细节。slowList 中包含了 250 个子组件。每个子组件都渲染 1ms，那么整个组件渲染就需要耗时至少 250ms.

在父组件中，我们把 deferred 传递给 Expensive

```html
<Expensive counter={deferred} />
```

那么此时表示，Expensive 的 UI 更新任务是低优先级。counter 对应的任务可以中断它的执行。


此时一个很明显的区别就是，counter 的 UI 变化变得更加流畅了。这是因为耗时被拆分到了多个子组件中，React 就有机会中断这些函数的执行，并执行优先级更高的任务，以确保高优先级任务的流畅。

如果你没有使用 React Compiler，你需要使用 memo 手动缓存 `Expensive`。

```javascript
export default memo(function Expensive({ text }) {
  // ...
});
```

当更新发生时，useDefferdValue 会首先使用旧值传递给组件。

```html
<Expensive counter={deferred} />
```

因此，当 counter 发生变化时，deferred 依然是旧值，那么此时，如果我们使用 memo 包裹，Expensive 的 props 就没有发生变化，我们可以跳过此次针对 Expensive 的更新。所以当我们快速点击时，Expensive 总是接受到旧值，它本身的渲染也会被中断，因此最终的表现结果是，当我们连续点击 7 次，counter 从 0 依次变成 7，而 deferred 会直接从 0 变成 7.

这跟 React 的性能优化策略有关。


### 3、**运行原理**

看了上面两个例子，肯定还是有一部分人会觉得很懵，不要急，接下来我们把运行原理分析一下，整个情况就清晰了。

useDeferredValue 会尝试将 UI 任务更新两次。

第一次，会给子组件传递旧值。此时 `Expensive` 接收到的 props 会与上一次完全相同。如果结合了 React.memo，那么该组件就不会重新渲染。该组件可以重复使用之前的渲染结果
> Compiler 编译之后不需要 memo

此时，高优先级的任务渲染会发生，渲染完成之后，将会开始第二次渲染。此时，将会传入刚才更新之后的新值。对于 `Expensive` 而言，props 发生了变化，整个组件会重新渲染。

我们通常会将已经非常明确的耗时任务标记为 deferred，因此，这些任务都被视为低优先级。当重要的高优先级更新已经完成，低优先级任务在第二次渲染时尝试更新...

在它第二次更新的过程中，如果又有新的高优先级任务进来，那么 React 就会中断并放弃第二次更新，去执行高优先级的任务。

> 注意：是中断，并放弃这次更新，所以表现出来的结果就是，中间会漏掉许多任务的执行

这样的运行机制有一个非常重要的好处。

那就是，如果你的电脑性能足够强悍，那么第二次的更新可能会快速完成，高优先级的任务来不及中断，那么我们的页面响应就是非常理想的。

但是如果我们的电脑性能比较差，第二次更新还没完成，新的高优先级任务又来了，那么就可以通过中断的方式，降级处理，保证重要 UI 的流畅，放弃低优先级任务。

> 在不同性能的设备上，有不同的反应，这个是跟防抖、节流的最重要的区别



### 4、**重新分析取消请求案例**

那我们回过头来，分析一下最开始的那个案例，重新看一眼代码

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs list.tsx -c
!from ./demo02/list.tsx
```

```tsx !!tabs Input.tsx -c
!from ./demo02/Input.tsx
```

```tsx !!tabs api.ts -c
!from ./demo02/api.ts
```
</CodeTabs>

这里我们将 promise 做为 state，当 promise 被重新赋值时，List 会经历两次更新。

首先点击事件触发，请求立即发生。promise 被改变。触发组件更新。

第一次更新时，deferred 使用旧值传参，此时对于 List 而言，api 没有发生变化。因此，利用这个机制，我们可以阻止 Suspense 直接渲染成 fallback.

在 Suspense 包裹之下，只有当接口请求成功之后，deferred 的第二次更新才会发生，因此，在这个过程中，如果我们快速进行第二次点击，可以直接取消上一次请求，让第二次更新来不及执行。此时新的请求发生。



### 5、**总结**

这种场景的最佳实践代码非常的简洁和优雅。写起来也很舒服，性能也非常强悍。但是理解起来会比较困难。因此想要做到灵活运用，还需要多多消化。

但是，等你彻底掌握它之后，你就会发现 React 19 在异步交互上真的太优雅了。这样的开发体验，是依赖 useEffect 完全比不了的。
