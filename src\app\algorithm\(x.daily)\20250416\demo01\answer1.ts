class TreeNode {
  val: number
  left: TreeNode | null
  right: TreeNode | null
  constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
    this.val = (val===undefined ? 0 : val)
    this.left = (left===undefined ? null : left)
    this.right = (right===undefined ? null : right)
 }
}

function preorderTraversal(root) {
  const res = []
  if (!root) {
    return res
  }
  // 定义栈数组
  const stack = []
  // 将根节点压入栈中
  stack.push(root)
  root.x = true // 标记节点是否已经访问过
  // 遍历栈，遍历的结束条件为栈数组被清空
  while(stack.length !== 0) {
    const top = stack[stack.length - 1]
    
    if (top.left && !top.left.x) {
      stack.push(top.left)
      top.left.x = true
    } else if (top.right && !top.right.x) {
      stack.push(top.right)
      top.right.x = true
    } else {
      const node = stack.pop()
      res.push(node.val)
    }
  }

  return res
};