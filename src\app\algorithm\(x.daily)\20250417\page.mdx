import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 练习题：比较两棵树是否相等

> 请勿第一时间看答案，先自己思考，思考不出来，再看答案


给你两棵二叉树的根节点 p 和 q ，编写一个函数来检验这两棵树是否相同。

如果两个树在结构上相同，并且节点具有相同的值，则认为它们是相同的。

![](/images/algorithm/daily/20250417/1.jpg)
![](/images/algorithm/daily/20250417/2.jpg)
![](/images/algorithm/daily/20250417/3.jpg)

### 题目要求

<Link href='https://leetcode.cn/problems/same-tree/description/'>原题：100. 相同的树</Link>

基于特殊的练习目的，请综合利用如下知识点来完成题目：

+ 1、双指针
+ 2、栈的执行顺序
+ 3、迭代，不要使用递归




首先，我们要约定 `p` `q` 分别为两棵树的根节点。当我们需要按照栈的方式对两棵树进行遍历时，那么，`p` `q` 就会变成栈的栈顶指针。

因此，在循环过程中，我们需要特别注意 `p` `q` 的移动变化。

我们约定一个栈结构，存储两棵树对应位置节点集合

```ts
let s = [[p, q]]
```

每一轮循环开始时，先判断是否相等。判断完之后，将当前节点标记为已访问。

```ts
if (p === null && q === null) {
  return true
}
if (p === null || q === null) {
  return false
}
if (p.val !== q.val) {
  return false
}

p && (p.x = true)
q && (q.x = true)
```

栈顶指针指的是栈结构中的最后一个元素，因此，我们需要使用 `s[s.length - 1]` 来重新获取栈顶指针，这样做的目的是为了兼容弹出栈时，栈指针往回移动的情况。

```ts
const top = s[s.length - 1]
p = top[0]
q = top[1]
```

往下移动时，基于深度优先遍历，需要优先考虑左结点是否存在，然后考虑右节点是否存在，如果存在，则移动栈顶指针的位置，并把当前节点入栈。

```ts
if ((p.left && !p.left.x) || (q.left && !q.left.x)) {
  // 在树结构中移动栈顶指针位置
  p = p.left
  q = q.left
  s.push([p, q])
  continue   
}
if ((p.right && !p.right.x) || (q.right && !q.right.x)) {
  p = p.right
  q = q.right
  s.push([p, q])
  continue;
}
```

如果都不存在，则需要回归查找，通过弹出栈的方式来做到

```ts
s.pop()
```

完整代码如下


<CodeTabs>
```ts !!tabs answer1.ts -c
!from ./demo01/answer1.ts
```
</CodeTabs>

### 总结

我的这种思路是利用了双栈顶指针的思路，完整的基于栈的顺序进行深度优先遍历，从而达到更快判断出两棵树存在差异的情况。在评论区中，也有其他模拟栈思路的解法，大家可以参考