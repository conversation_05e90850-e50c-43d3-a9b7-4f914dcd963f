export const r1 = [
  {
    type: 'tip',
    name: '基础语法'
  },
  {
    path: '3.var',
    name: '1、变量与引用',
    label: '限免'
  },
  {
    path: '4.move',
    name: '2、值的移动与指针的移动',
    label: '限免'
  },
  {
    path: '5.condition',
    name: '3、条件判断',
    label: '限免'
  },
  {
    path: '6.forloop',
    name: '4、for 循环',
    label: '限免'
  },
  {
    path: '7.while',
    name: '5、while 循环',
    label: '限免'
  },
  {
    path: '8.sliderwindow',
    name: '6、实践：滑动窗口',
    label: '限免'
  },
  {
    path: '9.recursion',
    name: '7、递归',
    label: '限免'
  },
  {
    path: '10.iteration',
    name: '8、迭代',
    label: '限免'
  },
  {
    path: '11.timecomplexity',
    name: '9、时间复杂度',
    label: '限免'
  },
  {
    path: '12.spacecomplexity',
    name: '10、空间复杂度',
    label: '限免'
  }
]
