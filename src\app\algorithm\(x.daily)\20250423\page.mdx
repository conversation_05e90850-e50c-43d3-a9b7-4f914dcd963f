import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 练习题：删除链表倒数第 N 个节点

<Link href="https://leetcode.cn/problems/remove-nth-node-from-end-of-list/description/">原题地址：19. 删除链表的倒数第 N 个结点</Link>

给你一个链表，删除链表的倒数第 `n` 个结点，并且返回链表的头结点。

![](/images/algorithm/daily/20250423/1.jpg)

![](/images/algorithm/daily/20250423/2.jpg)

```ts
/**
 * Definition for singly-linked list.
 * class ListNode {
 *     val: number
 *     next: ListNode | null
 *     constructor(val?: number, next?: ListNode | null) {
 *         this.val = (val===undefined ? 0 : val)
 *         this.next = (next===undefined ? null : next)
 *     }
 * }
 */

function removeNthFromEnd(head: ListNode | null, n: number): ListNode | null {
    
};
```

### 解题思路

...


