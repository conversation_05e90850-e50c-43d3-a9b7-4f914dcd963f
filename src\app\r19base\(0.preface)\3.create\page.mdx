import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'

首先，我们使用 vite 创建一个 React 项目

```bash
npm create vite
```

上面的命令执行完成之后，会引导你输入项目名称，我们将项目名称取为：`react19feature`

```bash
✔ Project name: react19feature
```

输入之后，按下回车，选择前端框架

```bash
    vanilla
    vue
 >  react
    react-ts
    preact
    lit
    svelte
```

我们选择 `react`，执行完毕之后，vite 会在当前目录中创建项目 `react19feature`，我们通过如下指令进入到项目文件

```bash
cd react19feature
```
然后用编辑器打开项目文件，在根目录的 `package.json` 文件中修改 `react` 与 `react-dom` 的依赖版本为 react 19.

```ts
"dependencies": {
  "react": "^19.0.0",
  "react-dom": "^19.0.0",
}
```

> 注意：如果已经正式发版，那么vite 创建的版本可能会直接更新为最新版而不需要我们手动修改

修改完成之后，执行如下指令安装依赖

```bash
npm i
```

然后执行如下指令运行项目

```bash
npm run dev
```

成功启动之后，我们可以在浏览器中输入 `http://localhost:5173` 访问到项目


<Demo01 />
<CodeTabs>

```tsx !!tabs app.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs react.svg -c
!from ./demo01/react.svg
```

```tsx !!tabs vite.svg -c
!from ./demo01/vite.svg
```

</CodeTabs>

由于我们项目需要用到 `require`，但是 vite 中使用 ES module 作为模块方案，因此并没有内置对CommonJS 的支持，如果你需要在项目中支持 `require` 语法引入模块，则需要做点其他的操作

首先，我们需要**安装如下依赖**

```bash
npm i vite-plugin-require-transform --save-dev
```

然后，在 `vite.config.js` 中新增如下配置

```ts vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
// !diff +
import requireTransform from 'vite-plugin-require-transform'


// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // !diff(1:3) +
    requireTransform({
      fileRegex: /.js$|.jsx$/,
    }),
  ],
})
```
> 如果你需要支持更多的文件后缀，则需要在 `fileRegex` 中添加，本项目仅支持 `.js` 与 `.jsx`

最后重启项目即可。

除此之外，如果你的项目中需要使用路由库，我们可以安装 `react-router`

```bash
npm i react-router
```

状态管理库 `zustand`

```bash terminal
npm i zustand
```