export const rx = [
  {
    type: 'tip',
    name: '每日刷题'
  },
  {
    path: '20250411',
    name: '和为 k 的最长子数组',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250412',
    name: '长度为 K 子数组中的最大和',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250413',
    name: '最大连续 1 的个数',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250414',
    name: '二叉树的最大深度',
    type: 'primary',
    label: '简单',
  },
  {
    path: '20250415',
    name: '平衡二叉树判断',
    type: 'primary',
    label: '简单',
  },
  {
    path: '20250416',
    name: '二叉树的后续遍历',
    type: 'primary',
    label: '简单',
  },
  {
    path: '20250417',
    name: '两棵树是否相等',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250418',
    name: '最长公共子序列',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250421',
    name: '合并两个有序链表',
    type: 'primary',
    label: '简单',
  },
  {
    path: '20250422',
    name: '判断链表中是否存在环',
    type: 'primary',
    label: '简单',
  },
  {
    path: '20250423',
    name: '删除链表倒数第 N 个节点',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250429',
    name: '颜色值转换',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250604',
    name: '栈排序',
    type: 'warning',
    label: '中等',
  },
  {
    path: '20250605',
    name: '最近的请求次数',
    type: 'primary',
    label: '简单',
  },
  {
    path: '20250606',
    name: '二叉树的层序遍历',
    type: 'warning',
    label: '今日',
  },
  {
    path: '20250607',
    name: '合并K个升序列表',
    type: 'danger',
    label: '困难',
  },
]