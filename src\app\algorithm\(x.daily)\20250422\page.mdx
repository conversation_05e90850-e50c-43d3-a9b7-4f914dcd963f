import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 练习题：判断链表中是否存在环

<Link href="https://leetcode.cn/problems/linked-list-cycle/description/">原题地址：141. 环形链表</Link>

给你一个链表的头节点 `head` ，判断链表中是否有环。

如果链表中有某个节点，可以通过连续跟踪 `next` 指针再次到达，则链表中存在环。 为了表示给定链表中的环，评测系统内部使用整数 `pos` 来表示链表尾连接到链表中的位置（索引从 0 开始）。注意：`pos` 不作为参数进行传递 。仅仅是为了标识链表的实际情况。

如果链表中存在环 ，则返回 `true` 。 否则，返回 `false` 。


**示例 1**

```ts
输入：head = [3,2,0,-4], pos = 1
输出：true
解释：链表中有一个环，其尾部连接到第二个节点。
```

![](/images/algorithm/struct/15.looplinked/7.jpg)

**示例 2**

```ts
输入：head = [1,2], pos = 0
输出：true
解释：链表中有一个环，其尾部连接到第一个节点。
``` 

![](/images/algorithm/struct/15.looplinked/8.jpg)

**示例 3**

```ts
输入：head = [1], pos = -1
输出：false
解释：链表中没有环。
```

![](/images/algorithm/struct/15.looplinked/9.jpg)

### 解题思路

...