import { useEffect, useRef } from 'react'
import functionPlot from 'function-plot'

export default function Demo02() {
  const input = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const bounds = input.current?.getBoundingClientRect()
    functionPlot({
      width: bounds?.width,
      target: '#timecomplexity_02',
      yAxis: {
        label: '时间复杂度',
        domain: [0, 10],
      },
      xAxis: {
        label: '输入规模',
        domain: [0, 5],
      },
      data: [
        {
          fn: 'x^2',
        },
      ],
    })
  }, [])

  return (
    <div id='timecomplexity_02' ref={input} className='w-full' />
  )
}