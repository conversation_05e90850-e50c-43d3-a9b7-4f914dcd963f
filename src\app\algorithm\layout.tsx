'use client'

import { FolderDown, CakeSlice, Home, SquareArrowUp } from 'lucide-react'
import { useRef, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import NavSider from 'components/nav-sider'
import Drawer from 'components/ui/modal/drawer'
import { LOGO } from 'app/components/nav-header'
import { getSubscribe } from 'app/service/api'
import { setSubscribe, ColumnKey, useLoginStore } from 'app/service/index'
import { reactversion, column_id, user_ids } from './config'
import { routers } from './router'

export default function ColumnLayout({ children }: any) {
  const pathname = usePathname()
  const column_key = pathname.split('/')[1] as ColumnKey
  const drawer = useRef<any>(null)

  const isLogin = useLoginStore(s => s.isLogin)
  const userinfo = useLoginStore(s => s.userinfo)

  useEffect(() => {
    if (!isLogin) return
    const vip = user_ids.includes(userinfo.user_id)
    if (vip) {
      return setSubscribe(column_key, 1)
    }
    getSubscribe(column_id).then(res => {
      setSubscribe(column_key, res.status)
    })
  }, [isLogin])

  return (
    <div className='pt-16 md:flex pb-12 md:pb-0'>
      <aside id='vp-aside' className='fixed top-0 left-0 bottom-0 overflow-y-scroll bg-gray-50 dark:bg-gray-800 hidden md:block'>
        <div className='px-6 w-full'>
          <div className='flex items-center pb-8 pt-24 sticky top-0 bg-gray-50 dark:bg-gray-800 border-b border-b-gray-200 dark:border-b-gray-700 mb-8'>
            <div className='flex items-center justify-between p-1 border mr-3 rounded-md border-blue-200 dark:border-blue-700'>
              <FolderDown className='text-blue-500 dark:text-blue-400' size={20} />
            </div>

            <div className='text-sm'>
              <div>当前版本</div>
              <div className='text-gray-500 dark:text-gray-400'>{reactversion}</div>
            </div>
          </div>
          <nav className='pb-8'>
            <NavSider routers={routers} />
          </nav>
        </div>
      </aside>
      <div id='vp-content'>
        {children}

        <div className='md:hidden fixed bottom-0 left-0 right-0 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex justify-around'>
          <div className='flex flex-col items-center' onClick={() => window.location.href = `/${column_key}`}>
            <Home size={16} />
            <span className=' text-sm mt-1'>专栏首页</span>
          </div>
          <div className='flex flex-col items-center' onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>
            <SquareArrowUp size={16} />
            <span className=' text-sm mt-1'>到顶</span>
          </div>
          <div className='flex flex-col items-center' onClick={() => drawer.current.show()}>
            <CakeSlice size={16} />
            <span className=' text-sm mt-1'>专栏目录</span>
          </div>
        </div>

        <Drawer ref={drawer} direction='left' width='60%'>
          <header className='h-16 flex items-center px-4'>
            <LOGO />
          </header>
          <nav className='bg-white dark:bg-gray-800 h-full py-8 overflow-scroll px-6' onClick={() => drawer.current.close()}>
            <NavSider routers={routers} />
          </nav>
        </Drawer>
      </div>
    </div>
  )
}
