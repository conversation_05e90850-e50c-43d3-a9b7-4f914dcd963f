import { useEffect, useRef } from 'react'
import HashMap from './hashMap'

export default function Demo01() {
  const input = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // 使用示例
    const map = new HashMap();
    console.log('xxx hashmap')

    const s1 = {
      id: 1001,
      name: '张三',
      grade: 100
    }
    const s2 = {
      id: 1002,
      name: '李四',
      grade: 100
    }
    const s3 = {
      id: 1003,
      name: '王五',
      grade: 100
    }
    const s33 = {
      id: 1033,
      name: '张三',
      grade: 100
    }
    map.set(1001, s1)
    map.set(1033, s33)
    map.set(1002, s2)
    map.set(1003, s3)

    console.log(map)

    console.log(map.values())
  }, [])

  return (
    <div id='timecomplexity_01' ref={input} className='w-full'>
    <div className="relative w-full h-[400px] bg-[#0a0a0a] overflow-hidden">
      {/* 网格背景 */}
      <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
        {Array.from({ length: 64 }).map((_, i) => (
          <div key={i} className="border border-gray-700/50" />
        ))}
      </div>

      {/* iOS 26 风格液态玻璃按钮 */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <button className="relative px-8 py-4 text-white rounded-2xl overflow-hidden group">
          {/* 背景模糊层 */}
          <div className="absolute inset-0 bg-white/5 backdrop-blur-2xl" />
          
          {/* 液态玻璃效果 */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-white/2 opacity-0 group-hover:opacity-100 transition-all duration-300" />
          
          {/* 边缘光晕 */}
          <div className="absolute inset-0 border border-white/10 rounded-2xl" />
          
          {/* 内容 */}
          <span className="relative z-10 font-medium">Hover Me</span>
        </button>
      </div>
    </div>
    </div>
  )
}