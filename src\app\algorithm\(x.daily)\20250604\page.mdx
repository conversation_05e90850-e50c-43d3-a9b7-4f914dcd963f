import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 练习题：栈排序

<Link href="https://leetcode.cn/problems/sort-of-stacks-lcci/description/">原题：栈排序</Link>

题目描述：

编写程序，对栈进行排序使最小元素位于栈顶。最多只能使用一个其他的临时栈存放数据，但不得将元素复制到别的数据结构（如数组）中。该栈支持如下操作：

+ `push`
+ `pop`
+ `peek`  当栈为空时，`peek()` 返回 -1。
+ `isEmpty` 当栈为空时，返回 true


**解题思路**
---

```ts
const stack = [3, 1, 2, 4]
```
**解题思路**

栈排序是指在**将元素推入到栈中时**，将元素按照升序或者降序的方式进行排序。这里有一个要求就是，排序的过程必须遵循栈的先进后出、后进先出的原则。

因此，栈排序必须利用另外一个辅助栈，来完成排序。

辅助栈的作用是，当新加入的元素，比辅助栈的栈顶元素大时，我们需要将现有栈中的元素依次放入辅助栈中，直到新元素比现有栈顶元素更小，此时，就可以将新元素直接放入栈中，然后从辅助栈把元素依次放回栈中。

这样，栈中的元素就按照升序排列了。

用图示来表示，就是这样的：

![](/images/algorithm/daily/20250604/1.png)


此时，元素 7 要进来，但是 7 明显不是最小的元素，所以，我们要从栈顶开始，把小于 7 的元素依次放入辅助栈中，直到栈顶元素大于 7 为止。

![](/images/algorithm/daily/20250604/2.png)

然后将 7 放入栈中，

![](/images/algorithm/daily/20250604/3.png)

然后从辅助栈中依次将元素放回栈中。

![](/images/algorithm/daily/20250604/4.png)

这样，栈中的元素就按照升序排列了。


代码如下

<CodeTabs>
```ts !!tabs Stack.ts -c
!from ./demo01/answer.ts
```
</CodeTabs>

