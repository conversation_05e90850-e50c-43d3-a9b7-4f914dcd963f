import { useRef } from 'react'

import { invertTree } from './answer'

export default function Demo01() {
  const input = useRef([2, 1, 3])
  const output = useRef([2, 3, 1])
  const k = useRef(4)

  return (
    <div className=''>
      <div className='text-sm text-gray-600 my-2'>输入</div>
      <div className='bg-gray-100 dark:bg-gray-800 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'>[{input.current.join(', ')}]</div>
      </div>

      <div className='text-sm text-gray-600 my-2'>输出</div>
      <div className='bg-gray-100 dark:bg-gray-800 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'>[{output.current.join(', ')}]</div>
      </div>
    </div>
  )
}