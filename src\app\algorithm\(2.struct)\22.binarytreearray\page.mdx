import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'

### 二叉树的数组表示法

二叉树的结构比较复杂，因此很难再代码中直接表示。因此，我们通常会使用数组来表示二叉树。

表达的方式，则是按照广度优先遍历的顺序，将二叉树的节点从左到右值依次填入数组中。

我们通过几个案例来学习一下二叉树的数组表示法。


例如，如下二叉树

![](/images/algorithm/struct/21.binarytree/3.png)

用数组表示为

```ts
// 输入
[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
```

再例如，如下二叉树


没有的节点，使用 null 来填充，例如下图中，节点 9 没有子节点，但是他又是左节点，因此，需要使用 null 来填充空位

![](/images/algorithm/daily/20250606/1.png)


```ts
// 表示为
[3, 9, 20, null, null, 15, 7]
```


如果是右边的节点没有，可以按照从左到右的顺序不填充


![](/images/algorithm/struct/22.binarytreearray/1.png)

```ts
// 表示为
[3, 9, 20, 9, 11]
```


但是，如果按照从左到右的循序，发现右边没有节点了，而左边更深层级还有节点，则需要依次填充，以确保左边节点的正确位置

![](/images/algorithm/struct/22.binarytreearray/2.png)

```ts
// 表示为
[3, 9, 20, 9, 11, null, null, 10, 13]
```


### 总结

使用数组来表达二叉树是算法题目中非常常见的做法，我们这里列举了一些常见的表达方式，大家也可以在题目中深入掌握。通常情况情况下，我们会使用 null 将二叉树填充为一个**完全二叉树**，以确保每个元素的位置都正确。