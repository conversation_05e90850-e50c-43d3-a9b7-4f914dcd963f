import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 二叉树的层序遍历

<Link href="https://leetcode.cn/problems/binary-tree-level-order-traversal/">原题：二叉树的层序遍历</Link>

题目描述：

给你二叉树的根节点 `root` ，返回其节点值的 层序遍历 。 （即逐层地，从左到右访问所有节点）。

![](/images/algorithm/daily/20250606/1.png)

```ts
// 输入
root = [3,9,20,null,null,15,7]

// 输出
[[3],[9,20],[15,7]]
```

```ts
// 输入
root = [1]

// 输出
[[1]]
```

```ts
// 输入
root = []

// 输出
[]
```

### 解题思路

这个题是二叉树的层序遍历，层序遍历就是按照层级来遍历二叉树。核心思路是**广度优先遍历**。完整的代码如下，具体的实现思路在注释中已经给出。

<CodeTabs>
```ts !!tabs bfs.ts -c
!from ./demo01/bfs.ts
```
</CodeTabs>



