export function maximumSubarraySum(nums: number[], k: number): number {
  let right = 0, 
      left = 0, 
      obj = {} as any, 
      ans = 0, 
      res = 0

  while (right < nums.length) {
    // 如果set存过  set就从left开始删  直到没存过nums[right]
    while (obj[nums[right]]) {
      obj[nums[left]] = false
      res -= nums[left++]
    }
    
    // set存当前的nums[right]
    obj[nums[right]] = true
    res += nums[right++]

    // 如果有k个了 取大值  删除left的
    if (right - left === k) {
      ans = Math.max(ans, res)
      obj[nums[left]] = false
      res -= nums[left++]
    }
  }
  return ans
};