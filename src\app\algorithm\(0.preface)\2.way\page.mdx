本专栏主要通过如下几个方式来降低算法的学习难度

**1、基础语法铺垫**

在学习具体的算法之前，我们会先对一些基础的语法进行深度的铺垫，重新完整的学习例如各种循环的方式、递归、内存、指针等基础知识，这样在学习具体的算法时，就可以更加的得心应手。


**2、大量图例**

有的数据结构与算法理解起来比较困难，因此我会首先通过图例让大家对理论有一个比较清晰的认识，然后再通过代码来实现。

![](/images/algorithm/struct/15.looplinked/4.jpg)

**3、学习当前最优解** 

**找到最优解，是学习算法真正核心**，因此，我们在学习算法的过程中，针对不同的场景，会有不同的解法，然后在对比这些不同的解法，找到适合场景的最优解。

许多人自己学习刷题时，效果不好，有一半的原因就是自己没有能力去找到最优解。而这恰好是面试官最感兴趣，也是最难自学得到的部分。


**4、直播讲解** 

为了确保大家都可以学会，每一个稍微复杂一点的算法与题目，我都会在群内直播讲解，及时答疑。