`for` 循环在算法当中的使用非常广泛，是学习算法的最重要基础语法之一。

### 1、基础语法

for 循环是一个 **在特定条件之下，重复执行内部逻辑（循环体）** 的一个语法逻辑，可以用如下的语法结构来表示：

```ts
for (初始化; 停止条件; 迭代条件) {
  // 循环体
}
```

+ 初始化的值是当前值，循环体执行时，会对当前值进行操作
+ 停止条件是一个判断条件，判断当前值是否满足条件，满足则停止循环
+ 迭代条件是每次**循环结束**后，对当前值进行的操作

> 迭代条件是循环体执行结束之后，才会执行的操作。这是大家在学习 for 循环时，最容易忽略的一个知识点，从而导致许多人在工作多年之后，依然无法得心应手的去处理一些边界条件

例如，一个案例

+ 初始化时设定一个变量 `i = 0`
+ 停止条件是 `i < 10`
+ 迭代条件是 `i++` （每次循环结束后，变量 `i` 的值加一）
+ 循环体是 `console.log(i)`

```ts
for (let i = 0; i < 10; i++) {
  console.log(i)        // 0 1 2 3 4 5 6 7 8 9      
}
```

> 需要特别注意，由于循环体执行结束之后，会执行迭代条件，因此，在最后一次循环体执行之后，变量 `i` 的值继续递增 `9 -> 10`。然后在停止条件判断时，`i < 10` 不满足，因此循环结束。面试时，经常会问，循环体执行了几次？最终 i 的值是多少？这也是许多人容易忽视的知识点


我们也可以把初始条件写在循环的外面，例如：

```ts
let i = 0

for (; i < 10; i++) {
  console.log(i)        // 0 1 2 3 4 5 6 7 8 9
}
```

**划重点**，如果判断条件变得更加复杂，我们也可以定义多个初始变量和迭代条件，例如：

```ts
for (let i = 0, j = 0; i < 10 && j < 10; i++, j++) {
  console.log(i, j)        // 0 0 1 1 2 2 3 3 4 4 5 5 6 6 7 7 8 8 9 9
}
```


### 2、变体

迭代条件可以是任意的表达式，他通常是在初始值的基础之上，进行变化。例如，递减

```ts
for (let i = 10; i > 0; i--) {
  console.log(i); // 从10倒数到1
}
```

当然，也可以每次迭代都多加一点

```ts
for (let i = 0; i < 10; i += 2) {
  console.log(i); // 输出 0, 2, 4, 6, 8
}
```

一个比较常见的用法是遍历数组

```ts
const fruits = ['apple', 'banana', 'orange'];

for (let i = 0; i < fruits.length; i++) {
  console.log(fruits[i]);
}
```

有的时候，我们可能会有**多个终止条件**，我们可以把范围最大的终止条件放到外面，然后把一些小的终止条件，放到循环体内部来执行，通过 **`break`** 来终止循环

```ts
for (let i = 0; i < 10; i++) {
  if (i === 5) break;
  console.log(i); // 输出 0-4
}
```

或者通过 **`continue`** 来跳过当前循环：当前的循环体逻辑不执行，直接进入下一次循环

```ts
for (let i = 0; i < 10; i++) {
  if (i % 2 === 0) continue;
  console.log(i); // 输出奇数 1,3,5,7,9
}
```

### 3、嵌套循环

在复杂场景下，嵌套循环是一个常用的技术手段。例如，在最长公共子序列、React 底层的事件循环中，都使用到了嵌套循环。

for 循环的嵌套循环往往是一个二维的数据结构。我们可以用图示来表达

![](/images/algorithm/base/6.forloop/1.jpg)

```ts
for (let i = 0; i < 5; i++) {
  for (let j = 0; j < 5; j++) {
    console.log(`${i}-${j}`);
  }
}
```

外层循环是 `i`，内层循环是 `j`。外层循环**每执行一次，内层循环就会执行一轮**。初学者要稍微花一点时间去感悟一下。


### 4、在算法中的运用

for 循环在算法中的运用非常广泛。例如，最简单的**线性搜索**

从数组中，找到目标值的索引

```ts
function linearSearch(arr, target) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === target) {
      return i; // 找到目标，返回索引
    }
  }
  return -1; // 未找到
}

// 使用示例
const numbers = [4, 2, 7, 1, 9];
console.log(linearSearch(numbers, 7)); // 输出: 2
```

字符串反转，也是一个常见的算法题目

输入：`hello` -> 输出：`olleh`

核心思路是定义一个索引指针从字符串的末尾往前移动，并记录每次移动的字符，最后把这些字符拼接起来

```ts
function reverseString(str) {
  let reversed = '';
  for (let i = str.length - 1; i >= 0; i--) {
    reversed += str[i];
  }
  return reversed;
}

// 使用示例
console.log(reverseString('hello')); // 输出: 'olleh'
```