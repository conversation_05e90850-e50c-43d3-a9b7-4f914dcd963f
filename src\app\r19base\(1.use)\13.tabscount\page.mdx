import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'


<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs tabs.tsx -c
!from ./demo01/tabs.tsx
```

```tsx !!tabs config.ts -c
!from ./demo01/config.ts
```

```tsx !!tabs list.tsx -c
!from ./demo01/list.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>

注意看这个案例。有的时候我们需要从 Suspense 包裹的子组件中获取数据。

我设计了一个随机数量的请求，每一次请求返回随机数量的列表，Tabs 中的按钮旁边需要显示当前列表的长度。

但是此时，和将列表数据直接维护到 state 中不同，此时我们在 state 中维护的是 promise ，然后我们使用 use 从 promise 中读取数据。所以只有子组件使用 use 读取才能获得列表的长度。这个时候，我们如何在保持现有解决方案的前提之下，在父组件中拿到列表的数据呢？


首先父组件要拿到子组件的数据，我们可以给子组件传一个事件进去，让子组件在得到数据时，执行这个事件回调。

```tsx -c
<Suspense fallback={<Skeleton />}>
  <List promise={promise} onCompleted={__completed} />
</Suspense>
```

子组件在执行 `onCompleted` 的时候，把父组件需要的数据作为参数返回出来即可

```tsx -c
function CurrentList({promise, onCompleted}) {
  const users = use(promise)
  onCompleted && onCompleted(users.length)

  return (...)
}
```

这里我们需要考虑的一个问题就是 `onCompleted` 的执行时机要怎么比较合适。因为如果 onCompleted 中执行逻辑在父组件中，子组件无法控制，因此父组件的执行逻辑有可能会导致子组件 re-render，因此我们可以简单使用 useEffect 来防止 onCompleted 反复执行

```tsx
function CurrentList({promise, onComplete}) {
  const users = use(promise)
  // !diff +
  useEffect(() => {
    onCompleted && onCompleted(users.length)
  // !diff +
  }, [users])

  return (...)
}
```

这样处理之后，我们就可以轻松在父组件中，无压力拿到子组件中的数据了。父组件的代码示例如下

```javascript
function __completed(number) {
  tabs[current].count = number
  changeTabs([...tabs])
}
```
