type HNode<V> = {
  key: number,
  value: V,
  next: HNode<V> | null
}

type Queue<V> = {
  length: number,
  last: HNode<V> | null
}

export default class HashMap<V> {
  private buckets: Queue<V>[];
  private size: number;

  constructor(size = 32) {
    this.buckets = new Array(size);
    this.size = size;
  }

  // 哈希函数
  hash(key: number) {
    return (key - 1000) % this.size;
  }

  // 插入键值对
  set(key: number, value: V) {
    const index = this.hash(key);
    const bucket = this.buckets[index]

    const node: HNode<V> = {
      key: key,
      value: value,
      next: null
    }
    
    // 如果桶是空的，初始化一个链表
    if (!bucket) {
      node.next = node
      this.buckets[index] = {
        length: 1,
        last: node
      };
      return
    }

    const last = bucket.last

    if (!last) {
      node.next = node
      bucket.last = node
      return
    }

    const first = last.next

    // 检查是否已存在相同的key
    let current = last
    while(true) {
      if (current.key === key) {
        current.value = value
        break
      }
      current = current.next!
      if (current === bucket.last) {
        break
      }
    }

    // add the node to last
    last.next = node
    node.next = first
    bucket.last = node
    bucket.length += 1
  }

  // 获取值
  get(key: number) {
    const index = this.hash(key);
    const bucket = this.buckets[index];
    
    if (!bucket) return undefined;

    if (!bucket.last) {
      return undefined
    }
    
    let current: HNode<V> = bucket.last
    while(true) {
      if (current.key === key) {
        return current.value
      }
      current = current.next as HNode<V>
      if (current === bucket.last) {
        break
      }
    }
    
    return undefined;
  }

  // 删除键值对
  delete(key: number) {
    const index = this.hash(key);
    const bucket = this.buckets[index];
    
    if (!bucket) return false;
    
    let prev = bucket.last
    let current = bucket.last

    if (current === null || prev === null) {
      return false
    }

    while(true) {
      if (current.key === key) {
        if (prev === current) {
          bucket.last = null
        } else {
          const next = current.next
          prev.next = next
        }
        return true
      }
      prev = current
      current = current.next as HNode<V>
      if (current === bucket.last) {
        break
      }
    }
    
    return false;
  }

  // 检查是否包含键
  has(key: number) {
    return this.get(key) !== undefined;
  }

  // 获取所有键
  keys() {
    const keys = [];
    for (const bucket of this.buckets) {
      if (bucket && bucket.last) {
        let current = bucket.last;
        do {
          keys.push(current.key);
          current = current.next!;
        } while (current !== bucket.last);
      }
    }
    return keys;
  }

  // 获取所有值
  values() {
    const values = [];
    for (const bucket of this.buckets) {
      if (bucket && bucket.last) {
        let current = bucket.last;
        do {
          values.push(current.value);
          current = current.next!;
        } while (current !== bucket.last);
      }
    }
    return values;
  }

  // 清空哈希表
  clear() {
    this.buckets = new Array(this.size);
  }
}
