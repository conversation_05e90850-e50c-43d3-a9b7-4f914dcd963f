import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

> 请勿第一时间看答案，先自己思考，思考不出来，再看答案

### 思考题：平衡二叉树判断

### 题目描述

给定一个二叉树，判断它是否是**平衡二叉树**.

平衡二叉树的定义：一个二叉树每个节点的左右两个子树的高度差的绝对值不超过 1。


例如下面三个图例，前两个是平衡二叉树，最后一个不是平衡二叉树。

![平衡](/images/algorithm/daily/20250415/1.jpg)

![平衡](/images/algorithm/daily/20250415/2.jpg)

![不平衡](/images/algorithm/daily/20250415/3.jpg)


<Link href="https://leetcode.cn/problems/balanced-binary-tree/description/">原题地址：leetcode 110</Link>

> 目前优先使用递归的思路来解答，如果对递归非常熟练，也可以使用其他新的思维来解答

### 解题思路

前面一个题，我们知道了如何去计算一个二叉树的最大深度，因此，如果我们要判断一棵树是否平衡，只需要利用上一题的结果，然后判断左右两个节点的深度差不超过 1 即可

我们先把上一题的答案拷贝过来

```ts
export function maxDepth(root: TreeNode | null): number {
  if (root === null) {
      return 0
  }
  return Math.max(maxDepth(root.left), maxDepth(root.right)) + 1
};
```

然后通过判断一个节点的深度差即可

```ts
export function isBalanced(root: TreeNode | null): boolean {
  if (root === null) {
    return true
  }
  const leftDepth = maxDepth(root.left)
  const rightDepth = maxDepth(root.right)
  
  return Math.abs(leftDepth - rightDepth) <= 1
}
```

不过我们还要注意一个细节，那就是，如果子树不平衡，那么整棵树也是不平衡的。因此，我们还需要递归的去判断子树是否平衡。

```ts
return Math.abs(leftDepth - rightDepth) <= 1 && isBalanced(root.left) && isBalanced(root.right)
```

完整代码如下

<CodeTabs>
```ts !!tabs answer.ts -c
!from ./demo01/answer.ts
```
</CodeTabs>




