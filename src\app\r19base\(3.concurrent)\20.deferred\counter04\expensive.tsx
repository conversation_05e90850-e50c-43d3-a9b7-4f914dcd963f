import { memo } from 'react'
import SlowItem from './slow-item'

const Expensive = ({ counter }: { counter: number }) => {
  const items = []
  for (let i = 0; i < 200; i++) {
    items.push(<SlowItem key={i} counter={counter} />);
  }

  return (
    <div className='mt-4 text-green-500'>
      <div>Deferred: {counter}</div>
      <ul className='h-32 hidden'>
        {items}
      </ul>
    </div>
  );
}

export default memo(Expensive)