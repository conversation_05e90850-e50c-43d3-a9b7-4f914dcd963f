import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'

### 1、**改动**

与之前的版本相比，在 React19 中，context 有一些细微的变化。主要体现在如下三个方面。

**1、删除旧版 Context**

旧版本的 Context 在 2018 年 10 月（v16.6.0）被废弃。但是为了保证平滑的升级，旧版代码一直沿用到了现在。在 React 19 中，这些代码会正式被删除。旧版本的 Context 仅在使用 contextTypes 和 getChildContext API 的类组件中可用。因此它的删除对现在的项目应该只会造成很小的影响。

如果你在项目中仍然使用了旧版 Context，你可以参考下面新旧版本的对比写法进行调整升级。

<CodeTabs>


```javascript !!tabs new.tsx -c
const FooContext = React.createContext();

class Parent extends React.Component {
  render() {
    return (
      <FooContext value='bar'>
        <Child />
      </FooContext>
    );
  }
}

class Child extends React.Component {
  static contextType = FooContext;

  render() {
    return <div>{this.context}</div>;
  }
}
```

```jsx !!tabs old.tsx -c
import PropTypes from 'prop-types';

class Parent extends React.Component {
  static childContextTypes = {
    foo: PropTypes.string.isRequired,
  };

  getChildContext() {
    return { foo: 'bar' };
  }

  render() {
    return <Child />;
  }
}

class Child extends React.Component {
  static contextTypes = {
    foo: PropTypes.string.isRequired,
  };

  render() {
    return <div>{this.context.foo}</div>;
  }
}
```

</CodeTabs>


**2、简化 Provider 的使用**

```javascript
const Context = createContext({})
```

在以前的使用中，我们需要使用 Context.Provider 来包裹子组件。

```tsx
<Context.Provider value={value}>
  {props.children}
</Context.Provider>
```

在 React19 中，我们可以直接使用 Context 来代替 Provider，从而让代表变得更加简洁。

```tsx
<Context value={value}>
  {props.children}
</Context>
```

**3、可以使用 use 获取 context**

以前的版本中，在组件内部我们使用 useContext 来获取 context 中的状态。

```tsx
// after
import { use } from 'react';

function MyComponent() {
  const theme = use(ThemeContext);
  // ...
```


### 2、**重学一次 context**


在 React 中，props 能够帮助我们将数据层层往下传递。但是，当数据传递太多层之后，不仅代码上比较繁琐，理解上也容易混乱不清。因此，我们需要一种能够**跨越组件层级**让直达子组件的数据传递方式，这就是 context.

context 表示组件实例在运行期间能够**直接读取**的状态和内容。他记录了内存中的活跃数据。我们可以将这些数据使用 `useState` 来定义。那么，context 中的数据更改，就会驱动所有使用到该数据的 UI 发生变化。


> context 的学习主要分为如下三个部分
>
> **一、** 如何创建 context
>
> **二、** 顶层组件中如何传递数据
>
> **三、** 子组件中如何获取数据


**一、如何创建 context**

我们可以使用 `createContext` 来创建 context.

```ts
// create
const SomeContext = createContext(defaultValue)
```

`defaultValue` 表示默认值。他可以作为数据的兜底结果。当你无法从 `value` 中读取具体的值时，则会使用 `defaultValue` 中的值。在代码运行过程中，默认值始终保持不变。如果没有默认值，我们至少需要传入一个 `null`。

`createContext` 执行之后的返回值，就是我们需要的 `context`。



**二、如何传递 context**

返回的 context 通常是一系列组件的顶层父组件。因此，在使用时，我们通常会首先定义该顶层父组件。

```tsx
function Provider(props) {
  const value = {...}
  return (
    <SomeContext value={value}>
      {props.children}
    </SomeContext>
  )
}
export default Provider
```

在该顶层父组件中，我们使用刚才创建的 `context` 作为父级标签，把子组件包起来。并作为渲染内容返回。

```tsx
<SomeContext value={value}>
  {props.children}
</SomeContext>
```

此处的 `value` 表示我们在上下文中定义好的值。我们可以自己随意定义你想要传递给子组件的所有数据/方法。

> 这些数据/方法通常被多个不同的子组件共同使用。否则我们没必要将数据/方法存储在 context 中。

```tsx
import { createContext } from 'react';

const ThemeContext = createContext('light');

function App() {
  const [theme, setTheme] = useState('light');
  // ...
  return (
    <ThemeContext value={theme}>
      <Page />
    </ThemeContext>
  );
}
```

此时，案例中 `Page` 组件的所有后代子组件，都可以直接获取 context 中的值，不管层级有多深。

> value 可以是任何类型，你可以根据自己的项目需要设计合适的数据类型。


**三、如何获取 context 中的值**

在任意被包裹的子组件中，我们可以使用 `use` 来获取 context 中的值。

```tsx
function Button() {
  // ✅ Recommended way
  const theme = use(ThemeContext);
  return <button className={theme} />;
}
```

获取方法非常简单，接下来，我们使用具体的实践案例来分享 context 的使用。

> 需要注意，多个 Context **可以嵌套使用**，只是在实践中，这种场景不算多见。


### 3、**案例：在弹窗中更改页面的值**

学习了 context 的知识之后，我们来结合之前自定义的弹窗组件，一起来实现一下页面最开始的那个案例。

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs context.tsx -c
!from ./demo01/context.tsx
```

```tsx !!tabs task.tsx -c
!from ./demo01/task.tsx
```

```tsx !!tabs editor.tsx -c
!from ./demo01/editor.tsx
```
</CodeTabs>

> 由于是演示案例，所以数据比较简单，组件层级也不够深，所以大家把重点关注在 context 的技术学习上即可。并非代表真实实践中需要这样使用。

首先，我们创建一个 context 文件，该文件中，我们会创建 Context，并在其中定义好需要传递到各子组件中的去的数据和方法

```tsx context.tsx -c
!from ./demo01/context.tsx
```

然后在父组件中，使用 Provider 包裹需要用到的子组件。

```tsx index.tsx -c
!from ./demo01/index.tsx
```

然后在各子组件中，使用 `use(Context)` 获取当前组件需要的数据和方法即可

```tsx -c
import { use } from 'react'
import {Context} from './context.jsx'

export default function Editor(props) {
  const {ref, ...other} = props
  const {task, updateTask} = use(Context)
  ...
```

这里比较有意思的一个地方是在 `editor.tsx` 文件中，对于 ref 的使用和处理，大家可以留心观察一下。
