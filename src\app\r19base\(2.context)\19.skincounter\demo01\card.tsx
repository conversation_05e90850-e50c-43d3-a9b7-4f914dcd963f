import { use, useState } from 'react'
import { Context } from './context'
import Switch from 'components/ui/switch'

export default function Card() {
  const { count, incrementer } = use(Context)
  const [theme, setTheme] = useState('theme-dark')
  const [number, setNumber] = useState(0)

  function onSwitch(bool: boolean) {
    setTheme(bool ? 'theme-light' : 'theme-dark')
    setNumber(number + 1)
    incrementer(count + 1)
  }

  return (
    <div className={theme}>
      <div className='context-card p-4 rounded transition'>
        <div className="title font-bold">Canary</div>
        <div className='mt-4'>The useActionState Hook is currently only available in React’s Canary and experimental channels. Learn more about release channels here. </div>
      </div>

      <div className='flex justify-between py-4 items-center'>
        <div className='mr-4 text-blue-500 dark:text-blue-400'>当前组件切换次数: {number}</div>
        <Switch onChange={onSwitch} />
      </div>
    </div>
  )
}
