import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'


### 哈希表

哈希表又称为散列表。哈希表是一种用于**存储键值对**（key-value pairs）的数据结构，它通过**哈希函数**将键映射到列表中一个位置来访问记录，从而实现快速的数据查找。

在前端的日常开发过程中，实际上我们都经常会用到哈希表的思维来解决问题。

以一个简单的例子为例。

已知，我们有一个空数组。

```ts
const arr = new Array();
```

然后，我们有 5 个学生，每个学生的学号是 1001 到 1005。

| 学号 | 姓名 | 成绩 |
| ---- | ---- | ---- |
| 1001 | 张三 | 90   |
| 1002 | 李四 | 80   |
| 1003 | 王五 | 70   |
| 1004 | 赵六 | 60   |
| 1005 | 孙七 | 50   |


现在，我们需要将这 5 个学生保存到数组中。

普通的思路，我们可以这样做

```ts
const students = [
  { id: 1001, name: '张三', grade: 90 },
  { id: 1002, name: '李四', grade: 80 },
  { id: 1003, name: '王五', grade: 70 },
  { id: 1004, name: '赵六', grade: 60 },
  { id: 1005, name: '孙七', grade: 50 },
]
```

这也是许多同学的常规做法，但是这里有一个问题就是，我们无法通过学号快速找到对应的学生。需要通过遍历数组的方式，来对比目标学号与数组中的每一项是否相同，时间复杂度为 $O(n)$。

```ts
const student = students.find(student => student.id === 1001); 
// 时间复杂度为 O(n)
```

此时，查询效率较低。

如果此时我们设计一个哈希函数，将学号映射到数组中的一个位置，那么我们就可以通过学号快速找到对应的学生。

```ts hash.ts
// 根据当前规则设计的一个比较契合的哈希函数，将学号映射到数组中的一个位置
function hash(id: number) {
  return id - 1001;
}
```

通过这个哈希函数，我们将学号映射到数组中的一个位置，其中

+ 1001 映射到 0
+ 1002 映射到 1
+ 1003 映射到 2
+ 1004 映射到 3
+ 1005 映射到 4

然后将数据分别保存到数组对应的下标中，此时，我们就可以通过学号快速找到对应的学生。

```ts
const student = students[hash(1001)]; 
// 时间复杂度为 O(1)
```

![](/images/algorithm/struct/24.hash/1.png)

这个过程有的同学一定很熟悉，类似的思路在日常开发工作中，你会不自觉的用到，虽然你自己并没有意识到这是哈希表的思维。

当然，我们也可以直接把学号作为数组的下标，来保存学生信息。

```ts
const students = [];
students[1001] = { id: 1001, name: '张三', grade: 90 };
students[1002] = { id: 1002, name: '李四', grade: 80 };
students[1003] = { id: 1003, name: '王五', grade: 70 };
students[1004] = { id: 1004, name: '赵六', grade: 60 };
students[1005] = { id: 1005, name: '孙七', grade: 50 };


function hash(id: number) {
  return id;
}

const student = students[hash(1001)]; 
// 时间复杂度为 O(1)
```

但是这样，就会造成数组中存储的元素不连续，从而导致空间浪费。


### 总结

这种 hash 函数的设计理论，叫做直接定址法。适合关键字连续分布的情况，如果关键字不连续，则会造成空间浪费。

```ts hash.ts
// 根据当前规则设计的一个比较契合的哈希函数，将学号映射到数组中的一个位置
function hash(id: number) {
  return id - 1001;
}
```

下一章，我们继续讨论关键词不连续的情况应该如何设计哈希函数，以及哈希表的冲突问题。