class TreeNode {
  val: number
  left: TreeNode | null
  right: TreeNode | null
  constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
    this.val = (val===undefined ? 0 : val)
    this.left = (left===undefined ? null : left)
    this.right = (right===undefined ? null : right)
 }
}

function preorderTraversal(root: TreeNode | null): number[] {
  const res: number[] = []
  if (!root) {
      return res
  }
  // 定义栈数组
  const stack: TreeNode[] = []
  // 将根节点压入栈中
  stack.push(root)
  // 遍历栈，遍历的结束条件为栈数组被清空
  while(stack.length !== 0) {
    // 遍历时首先让节点直接出栈，此时是利用了巧妙的方式避免了重复判断，
    // 所以最终的执行上与栈的出栈入栈时机有细微差异，但执行顺序是相同的。不得不利用这个方式是由于我们没有区分左节点右节点是否已经入栈，
    const node = stack.pop()
    // 如果节点为空，直接跳过
    if (!node) {
      continue
    }
    // 记录节点值
    res.push(node.val)
    // 注意此时的顺序，为了保证先 pop 左结点，所以先压入右结点
    // 如果右节点存在，则将右节点压入栈中
    if (node.right) {
      stack.push(node.right)
    }
    // 如果左节点存在，则将左节点压入栈中
    if (node.left) {
      stack.push(node.left)
    }
  }
  
  return res
};