class ListNode {
  val: any;
  next: any;
  prev: any;
  constructor(val: any) {
      this.val = val;
      this.next = null;
      this.prev = null;
  }
}

/* 基于链表实现的队列 */
class LinkedListQueue {
  head: ListNode | null; // 头节点
  tail: ListNode | null; // 尾节点
  length = 0;

  constructor() {
    this.head = null;
    this.tail = null;
  }

  /* 获取队列的长度 */
  get size() {
    return this.length
  }

  /* 判断队列是否为空 */
  isEmpty() {
    return this.size === 0;
  }

  /* 尾部入队 */
  tail_push(num: any) {
    // 在尾节点后添加 num
    const node = new ListNode(num);
    // 如果队列为空，则令头、尾节点都指向该节点
    if (!this.head) {
      this.head = node;
      this.tail = node;
      // 如果队列不为空，则将该节点添加到尾节点后
    } else {
      node.next = this.tail;
      this.tail!.prev = node;
      this.tail = node;
    }
    this.length++;
  }

  /* 尾部出队 */
  tail_pop() {
    const num = this.tail_peek();
    // 删除头节点
    this.tail = this.tail!.next;
    this.tail!.prev = null;
    this.length--;
    return num;
  }

  /* 头部入队 */
  head_push(num: any) {
    const node = new ListNode(num);
    if (!this.head) {
      this.head = node;
      this.tail = node;
    } else {
      node.prev = this.head;
      this.head!.next = node;
      this.head = node;
    }
    this.length++;
  }

  /* 头部出队 */
  head_pop() {
    const num = this.head_peek();
    // 删除头节点
    this.head = this.head!.prev;
    this.head!.next = null;
    this.length--;
    return num;
  }

  /* 访问队首元素 */
  head_peek() {
    if (this.length === 0) throw new Error('队列为空');
    return this.head!.val;
  }

  /* 访问队尾元素 */
  tail_peek() {
    if (this.length === 0) throw new Error('队列为空');
    return this.tail!.val;
  }

  /* 将链表转化为 Array 并返回 */
  toArray() {
    let node = this.tail;
    const res = new Array(this.length);
    for (let i = 0; i < res.length; i++) {
      res[i] = node!.val;
      node = node!.next;
    }
    return res;
  }
}