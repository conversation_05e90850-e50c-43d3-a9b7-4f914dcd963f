class MyStack {
  queue: Array<number>
  help: Array<number>
  constructor() {
    this.queue = []
    this.help = []
  }

  push(x: number): void {
    while(this.queue.length) {
      this.help.unshift(this.queue.pop()!)
    }
    this.queue.push(x)
    while(this.help.length) {
      this.queue.unshift(this.help.pop()!)
    }
  }

  pop(): number {
    if (this.queue.length === 0) {
      throw new Error('栈为空')
    }
    return this.queue.pop()!
  }

  top(): number {
    return this.queue[this.queue.length - 1]
  }

  empty(): boolean {
    return !this.queue.length
  }
}