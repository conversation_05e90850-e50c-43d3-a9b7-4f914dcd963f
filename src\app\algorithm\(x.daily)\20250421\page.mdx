import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 练习题：合并两个有序链表

<Link href="https://leetcode.cn/problems/merge-two-sorted-lists/description/">原题地址：21. 合并两个有序链表</Link>

将两个升序链表合并为一个新的**升序**链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。 

![](/images/algorithm/daily/20250421/1.jpg)

```ts
输入：l1 = [1, 2, 4], l2 = [1, 3, 4]
输出：[1, 1, 2, 3, 4, 4]
``` 


```ts
/**
 * Definition for singly-linked list.
 * class ListNode {
 *     val: number
 *     next: ListNode | null
 *     constructor(val?: number, next?: ListNode | null) {
 *         this.val = (val===undefined ? 0 : val)
 *         this.next = (next===undefined ? null : next)
 *     }
 * }
 */

function mergeTwoLists(list1: ListNode | null, list2: ListNode | null): ListNode | null {
    
};
```

**解题思路**

本题的解题思路非常简单，只需要定义两个指针，分别指向两个链表的头节点，然后比较两个指针所指向的节点值，将较小的节点加入到新的链表中，然后移动指针继续比较，直到两个链表都遍历完。

这里主要考察的是大家对于链表操作的熟练程度。

步骤如下

首先，定义一个虚拟头节点，用于存储合并后的链表。并定义一个指针，用于存储合并后的链表的末尾节点。

```ts
const r = new ListNode(-1)
let current = r
```

然后，定义两个指针，分别指向两个链表的头节点。

```ts
const p1 = list1
const p2 = list2
```

然后利用 while 循环，通过 `node.next` 来移动指针。循环体中，比较两个指针所指向的节点值，将较小的节点加入到新的链表中，然后移动指针继续比较，直到两个链表都遍历完。

```ts
while(p1 && p2) {
  if (p1.val < p2.val) {
    current.next = p1
    p1 = p1.next
  } else {
    current.next = p2
    p2 = p2.next
  }
  current = current.next
}
```

最后，将剩余的节点加入到新的链表中。

```ts
current.next = p1 || p2
```

最后，返回合并后的链表

完整代码如下所示

```ts
/**
 * Definition for singly-linked list.
 * class ListNode {
 *     val: number
 *     next: ListNode | null
 *     constructor(val?: number, next?: ListNode | null) {
 *         this.val = (val===undefined ? 0 : val)
 *         this.next = (next===undefined ? null : next)
 *     }
 * }
 */

function mergeTwoLists(list1: ListNode | null, list2: ListNode | null): ListNode | null {
  const r = new ListNode(-1)
  let current = r

  let p1 = list1
  let p2 = list2

  while(p1 && p2) {
    if (p1.val < p2.val) {
      current.next = p1
      p1 = p1.next
    } else {
      current.next = p2
      p2 = p2.next
    }
    current = current.next
  }

  current.next = p1 || p2
  return r.next
};
```

注意最后返回的链表是 `r.next`，因为 `r` 是虚拟头节点。因此返回的结果中不包含虚拟头节点。