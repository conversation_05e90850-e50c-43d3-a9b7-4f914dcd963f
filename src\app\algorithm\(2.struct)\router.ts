export const r2 = [
  {
    type: 'tip',
    name: '基础数据结构'
  },
  {
    path: '13.array',
    name: '1、数组',
    label: '限免'
  },
  {
    path: '14.linked',
    name: '2、链表',
    label: '限免'
  },
  {
    path: '15.looplinked',
    name: '3、环形链表',
    label: '限免'
  },
  {
    path: '16.stack',
    name: '4、栈',
  },
  {
    path: '17.doublestack',
    name: '5、双栈',
  },
  {
    path: '18.queue',
    name: '6、队列',
  },
  {
    path: '19.deque',
    name: '7、双端队列',
  },
  {
    path: '20.queuetostack',
    name: '8、队列实现栈',
  },
  {
    path: '21.binarytree',
    name: '9、二叉树',
  }, 
  {
    path: '22.binarytreearray',
    name: '10、二叉树的数组表示法',
  },
  {
    path: '23.heap',
    name: '11、堆',
  },
  {
    path: '24.hash',
    name: '12、哈希表',
  },
  {
    path: '25.hashcollision',
    name: '13、哈希碰撞',
  },
  {
    path: '26.hashfunc',
    name: '14、哈希函数的运用',
  }
]
