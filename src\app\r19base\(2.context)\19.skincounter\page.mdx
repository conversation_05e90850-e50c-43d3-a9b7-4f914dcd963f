import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'

### 1、**皮肤切换方案**

利用 CSS 变量来实现皮肤切换，是目前最简单优雅的方案，也是最流行的方案。

CSS 变量又称之为**自定义属性**。他已经在主流浏览器中被普遍支持，我们可以在许多项目中使用该特性。我们熟知的 antd 中就大量运用了自定义属性。

声明一个自定义属性，需要以 `--` 开头，属性值可以是任何有效的 CSS 值。

```css
element {
  --main-bg-color: brown;
}
```
> 注意理解这句话：**自定义属性和其他属性一样，是写在规则集之内的。** 因此，它的改变，也能触发 transition 动画的执行

并且要注意的是，规则集所指定的选择器定义了自定义属性的可见作用域。通常的最佳实践是定义在根伪类 :root 下，这样就可以在 HTML 文档的任何地方访问到它了

```css
:root {
  --main-bg-color: brown;
}
```

当然，我也应该根据实践运用灵活选择作用域。

在本案例中，我们仅定义一个对当前代码生效的自定义属性。

```css
@layer context_count {
  .theme-dark {
    --text-color: rgba(255, 255, 255, 0.8);
    --bg-color: rgba(0, 0, 0, 0.8);
    --border-color: rgba(0, 0, 0, 0.1);
  }
  .theme-light {
    --text-color: rgba(0, 0, 0, 0.88);
    --bg-color: rgba(0, 0, 0, 0.02);
    --border-color: rgba(0, 0, 0, 0.1);
  }
}
```

`@layer` 表示降低当前属性的优先级，从而不影响其他任何元素的样式。哪怕是同名的也不会受到影响，此时我们将与皮肤属性相关的都自定义在主题中，不同的主题对应不同的属性。

然后运用到具体的元素上

```css
.context-card {
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}
```

此时，我们只需要构建如下所示的父子关系，然后更改父元素的 className，就可以轻松做到主题切换

```html
<!-- 所有使用了主题自定义变量的元素，是 `.theme-light` 的子元素 -->
<div class='theme-light'>
  <div class='context-card'></div>
</div>
```
切换主题时，只需要更改父元素的 class 即可。将其修改成另外一个配置好的主题。

有了这些皮肤切换的基础知识之后，我们来结合 context 实现一个皮肤切换并统计切换总次数的案例。


### 2、**多个模块皮肤切换总次数统计**

首先，我们要创建一个 context，并在 context 中准备好可能会用到的全局数据。

```jsx
import {createContext, useState} from 'react'

export const Context = createContext({})

export default function Provider({ children }) {
  const [count, incrementer] = useState(0)

  const value = {
    count,
    incrementer
  }

  return (
    <Context value={value}>{children}</Context>
  )
}
```

然后，我们要在顶层父组件中，使用 Provider 将所有的子组件包裹起来，这里也顺带引入皮肤切换相关的 css 文件

```jsx
import Provider from './context'
import Total from './Total'
import Card from './Card.jsx'
import './skin.css'

export default function App() {
  return (
    <Provider>
      <Total />
      <Card />
      <Card />
      <Card />
    </Provider>
  )
}
```

最后，我们只需要在子组件中，使用 use 读取对应的数据和方法

```jsx
import {use, useState} from 'react'
import {Context} from './context'

export default function Total() {
  const {count} = use(Context)
  return (
    <div>
      <h3 className='font-bold'>Total Switch Count</h3>
      <p className='!text-gray-400'>这里统计的是所有组件的切换次数: {count}</p>
    </div>
  )
}
```

演示效果、完整代码如下

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs context.tsx -c
!from ./demo01/context.tsx
```

```tsx !!tabs card.tsx -c
!from ./demo01/card.tsx
```

```tsx !!tabs total.tsx -c
!from ./demo01/total.tsx
```
</CodeTabs>



### 3、**皮肤切换思路的扩展**

我们可以把皮肤切换的方案，扩展到类似于 Switch 组件的实现上。在 Switch 组件中，当我们点击时，除了有颜色相关的样式切换之外，还有元素的位置要发生变化，因此，我们只需要额外多设计一个自定义属性就可以用完全一样的思路来实现这个组件了。

代码如下，非常的简洁。演示效果请看上面的皮肤切换案例。

<CodeTabs>
```jsx !!tabs index.jsx -c
import {useState} from 'react'
import './index.css'

export default function Switch(props) {
  const {value = false, onChange} = props
  const [checked, toggle] = useState(value)

  const cls = checked ? 'switch right' : 'switch left'

  function __click(value) {
    toggle(value)
    onChange && onChange(value)
  }

  return (
    <div className={cls} onClick={() => __click(!checked)}>
      <div className='circle'></div>
    </div>
  )
}
```


```css !!tabs index.css -c
@layer switch {
  .left {
    --bg-color: rgba(0, 0, 0, 0.1);
    --x: 0;
  }

  .right {
    --bg-color: rgba(0, 0, 0, 0.9);
    --x: 24px;
  }
}

.switch {
  @apply p-[1px] border w-14 rounded-full cursor-pointer transition;
  background-color: var(--bg-color);
}

.switch .circle {
  @apply rounded-full w-6 h-6 transition;
  background-color: white;
  transform: translateX(var(--x));
}
```
</CodeTabs>



