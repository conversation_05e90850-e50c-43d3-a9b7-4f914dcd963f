class TreeNode {
  val: number
  left: TreeNode | null
  right: TreeNode | null
  constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
    this.val = (val===undefined ? 0 : val)
    this.left = (left===undefined ? null : left)
    this.right = (right===undefined ? null : right)
 }
}

function bfs(root: TreeNode | null): number[] {
  const res: number[] = []
  if (!root) {
      return res
  }
  // 定义队列数组
  const queue: TreeNode[] = []
  // 将根节点压入队列中
  queue.unshift(root)
  // 遍历队列，遍历的结束条件为队列数组被清空
  while(queue.length !== 0) {
    // 遍历时首先让节点直接出队，此时是利用了巧妙的方式避免了重复判断，
    // 所以最终的执行上与队列的出队入队时机有细微差异，但执行顺序是相同的。不得不利用这个方式是由于我们没有区分左节点右节点是否已经入队
    const node = queue.pop()!
    // 如果节点为空，直接跳过
    if (!node) {
      continue
    }
    // 如果左节点存在，则将左节点入队
    if (node.left) {
      queue.unshift(node.left)
    }
    // 如果右节点存在，则将右节点入队
    if (node.right) {
      queue.unshift(node.right)
    }
    // 记录/访问节点值
    res.push(node.val)
  }
  return res
};