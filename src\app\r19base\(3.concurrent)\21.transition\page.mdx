import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'

<Demo01 />

useTransition 与 useDeferredValue 有几乎一致的作用。他们都可以降低 UI 任务更新的优先级。只是在语法上有一些不太一样。因此这一章我们主要重点是介绍 useTransition 的语法，以及它与 useDeferredValue 的细微差异。

```ts -c
// 基础用法
const [isPending, startTransition] = useTransition()
```

`startTransition` 可以标记一个或者多个状态的 `set` 方法，将他们标记为 `transition`，也就是调低他们更新的优先级。

> 但是这里需要注意的是，被调低的不是 set 方法本身的执行，而是其对应的 UI 更新。

`isPending` 表示是否还有未完成的 UI 更新任务。我们可以利用这个状态来判断请求是否正在发生。来看一个完整的演示案例，点击按钮更新数据时，注意观察交互。

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo01/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>


但是这里需要注意的是，优先级被调低的不是 set 方法本身的执行，而是其对应的 UI 更新。因此当我们连续点击 10 次按钮时，update 就紧跟着会执行 10 次。只是在目前的 Suspense 的机制中，会将 10 次接口请求串行执行

> 这个机制目前存在争议，可能会在未来发生改变

当我试图使用这种方案取消上一次请求时，我们会发现并没有效果，这里的原因是因为状态 promise 在 update 执行完之后，就已经交给 Suspense 的机制去处理了，React 并没有支持这种中断机制。

```javascript
function __handler() {
  startTransition(() => {
    promise.cancel()
    update(getMessage())
  })
}
```


因此，我们可以想办法在 promise 交给 Suspense 处理之前中断请求，在上一章中，我们使用了 useDeferredValue 的机制做到了这个事情。

```javascript
export default function App() {
  const [promise, update] = useState(fetchListWithCancel)
  const deferred = useDeferredValue(promise)


  function __inputChange(e) {
    promise.cancel()
    update(fetchListWithCancel())
  }
  ...
```

这里我们发现，我们第一时间交给 Suspense 的处理的并不是 promise，而是 deferred，因此我们可以在这之前，中断请求。


除了这个区别之外，useTransition 可以在组件顶层同时将多个 state 的 set 方法设置为低优先级。这一点是 useDeferredValue 做不到的。

```jsx
function TabContainer() {
  const [isPending, startTransition] = useTransition();
  const [tab, setTab] = useState('about');
  const [other, setOther] = useState(false);

  function selectTab(nextTab) {
    startTransition(() => {
      setTab(nextTab);
      setOther(true)
    });
  }
  // ...
}
```

useDeferredValue 可以在子组件中，设置来自 props 的状态延迟。这个也是 useTransition 不方便做到的

```js
export default function Ex(props) {
  const deferred = useDeferredValue(props.value)
  ...
}
```

最后直接来看这个案例，大家可以通过连续快速点击多次 Tab 按钮，来验证接口是否被取消


<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs tabs.tsx -c
!from ./demo02/tabs.tsx
```

```tsx !!tabs list.tsx -c
!from ./demo02/list.tsx
```

```tsx !!tabs config.ts -c
!from ./demo02/config.ts
```

```tsx !!tabs api.ts -c
!from ./demo02/api.ts
```
</CodeTabs>
