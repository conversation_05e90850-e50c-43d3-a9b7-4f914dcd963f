import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'


Tab 切换的过程中，请求接口也是日常开发中非常常见的交互方式。不过 tabs 切换的情况比较多，很考验开发者的个人能力。我们准备了三个不同的场景以供大家直接学习。

第一个案例比较简单。是**多对一**：多个 tab 按钮，对应一个组件。具体表现为按钮切换时，该组件本身重新获取数据渲染。演示效果如右侧案例所示。

首先，我们需要先封装一个 Tab 切换按钮。具体的封装代码可以查看右面的 `Tabs.jsx` 文件。

然后使用一下，检测一下效果

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs tabs.tsx -c
!from ./demo01/tabs.tsx
```

```tsx !!tabs config.ts -c
!from ./demo01/config.ts
```
</CodeTabs>

这里需要注意观察的是，案例中我们对 tabs 数据和 current 当前选中项的一个管理方式。在使用过程中，我们可以尽量减少对于 state 的使用，能不用就不用。但是许多人在开发过程中会非常依赖于 state，管理不善时，可能会导致数据的大量冗余 re-render 产生。这里当我们切换点击时，会修改两个数据，但是最终只有一个 state 变化。


接下来的事情就比较简单了，跟之前的案例一样，只需要在切换时，通过改变 promise 的方式请求接口即可。

所以我们需要声明一个新的状态 promise

```tsx
const [current, switchToSelected] = useState(0)
// diff +
const [promise, update] = useState(getUsersInfo)
```

点击时重新请求并修改 state promise 即可

```tsx
function __handler(index: number) {
  tabs[current].current = false
  tabs[index].current = true
  switchToSelected(index)

  // !diff(1:2) +
  promise.cancel()
  update(getUsersInfo())
}
```

这里我们依然需要取消上一次请求是因为可能有的使用者会连续快速切换，我们可以通过取消为完成请求的方式让页面响应变得合理与流畅。

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs tabs.tsx -c
!from ./demo02/tabs.tsx
```

```tsx !!tabs config.ts -c
!from ./demo02/config.ts
```

```tsx !!tabs list.tsx -c
!from ./demo02/list.tsx
```

```tsx !!tabs api.ts -c
!from ./demo02/api.ts
```
</CodeTabs>