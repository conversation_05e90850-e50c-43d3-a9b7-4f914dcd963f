本专栏主要通过如下几个方式来降低算法的学习难度

### 前端速刷算法方法论

算法题目非常多，我们不可能将所有的题目都学习一遍，因此，我们需要掌握一些速刷算法的方法论。这些方法论可以让我们在有限时间内，获得最大的学习收益。

**1、算法规律**

许多算法都是基于特定的规律来解决问题。

因此，多刷题的作用，是掌握更多的规律。而这些规律之间，耦合性比较小。

也就意味着，我们可以**零散**的学习这些规律，不用试图一次性学完所有的规律，然后通过刷题来巩固对这些规律的掌握。


**2、掌握基础语法**

基础不牢，地动山摇！

因此，**有限掌握基础知识，而不是一上来就刷题**

算法中，我们经常会用到各种基础语法，除了基本的 JS 语法之外，例如循环、递归、内存、指针等基础知识，这些基础语法是算法学习的基础，因此，我们需要专门花时间直接掌握这些基础语法。

在**图解算法**的小册中，我会专门用两个个大章来为大家介绍这些基础知识。具体的基础内容，大家可以在专栏目录中查看


**3、前端最容易出现的题目类型**

大厂出题的大方向的原则，是尽量出可能在**前端实践**中，能遇到的类型。切合前端实践的。

因此，我们可以圈定如下的范围来有限刷题，预计可以覆盖 80% 的题型

+ 考察数组排序等运用  -> 日常中常见
+ 考察栈  -> 函数调用栈
+ 考察队列 -> 事件循环、Promsie、事件队列、setTimeout 等，是高频题目
+ 考察链表 -> React 底层原理特意出现
+ 考察优先级队列 -> 优先级队列是 React 底层调度机制的核心算法之一，也是利用调度机制解决性能问题的核心算法之一
+ 考察二叉树 -> 二叉树是队列、栈、递归、优先级队列的综合场景，因此二叉树是高频题目
+ 考察二分搜索法 -> 在低代码项目、渲染引擎等高端场景中，二分搜索法是优化性能的核心要点
+ 考察滑动窗口 -> TCP 协议的一种应用，用于数据传输时的流量控制
+ 考察最长公共递增子序列 -> 在 Vue 的 diff 算法中出现，在编辑器的 diff 优化中能用到
+ 考察双指针与队列、广度优先遍历 -> 在 V8 垃圾回收机制的 GC 复制算法中出现
+ 考察递归与深度优先遍历 -> 在 DOM 树、 V8 的根搜索算法中出现

> 除此之外，还有一些基础题型，单纯就是为了考察基础知识而出现的。一般都比较简单，这里不单独列出来


**4、先理解在刷题**

大家在刷题的时候，经常犯的一个错误就是，总是试图自己把题目思考出来。实际上，这里有许多负面影响

+ 非常浪费时间
+ 极大的打击学习积极性和信心，甚至容易自我怀疑
+ 刷不动，容易导致学习停滞

我们的学习过程中，不需要花过多的时间去训练自己的思维能力。

因此，正确的做法是：先思考 10 分钟，如果 10 分钟没有思路，就直接去看题解。看完题解理解了之后，再自己写一遍，就可以提交了。

**切记：不要带着证明自己思维能力的心态去刷题！这毫无意义**

**5、每日复盘总结**

每天一定要花时间，快速复盘一下昨天的学习内容，深化和强化记忆。由于算法是比较底层的基础知识，因此，并不是完全依靠逻辑推理，而是有挺大一部分是需要靠记忆的。所以，记忆在算法的学习中是比较重要的一部分。


