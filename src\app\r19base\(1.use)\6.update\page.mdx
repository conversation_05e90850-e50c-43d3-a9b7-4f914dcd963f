import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'


接下来，我们将会以大量的实践案例来展开 React 19 新 hook 的运用。

本文模拟的实践案例为**点击按钮更新数据**。这在开发中是一个非常常见的场景。

案例的视觉表现为：初始化时没有请求，所以组件显示为空数据样式。当我们点击按钮时请求一条数据，数据更新，请求成功之后显示更新之后的内容。

案例完成之后的最终演示效果如下

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo01/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>

接下来，我们直接用 React 19 新的开发方式来完成这个需求。


### 1、**基础实现**

首先创建一个方法用于请求数据，请确保该方法执行会返回一个 promise 对象。

```ts api.tsx -c
!from ./demo01/api.ts
```

这里一个非常关键的地方就在于，当我们要更新的数据时，我们不再需要设计一个 `loading` 状态去记录数据是否正在发生请求行为，因为 `Suspense` 帮助我们解决了 Loading 组件的显示问题。

与此同时，`use()` 又帮助我们解决了数据获取的问题。这个时候，好像我们也不需要设计一个状态去存储数据。

那么我们应该怎么办呢？

这里有一个**非常巧妙**的方式，就是把**创建的 promise 作为状态值**来触发组件的重新执行。每次点击，我们都需要创建新的 promise

代码如下

```jsx
// 记住这个初始值
const [promise, update] = useState()
```

这个时候，当我们点击事件执行时，则只需要执行如下代码去触发组件的更新即可。

```ts 
function __handler() {
  // 每次点击，都会创建新的 promise 对象
  update(getMessage())
}
```

`getMessage()` 执行，新的请求会发生。他的执行结果，又返回了一个新的 promise.

因此，点击之后会创建的新 promise 值，promise 此时就会作为状态更改触发组件的更新。

完整代码如下

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo01/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>

接下来，我们还需要进一步分析一下这个案例。


### 2、**案例分析**

这里我们需要注意观察两个事情。

一个是观察当前组件更新，更上层的父组件是否发生了变化。我们可以在 `App` 组件中执行一次打印。

发现，当我们重新请求时，当前组件更新，但是上层组件并不会重新执行。

很显然：**更简洁的状态设计，有利于命中 React 默认的性能优化规则**。

更简洁的状态设计，也是 React 19 所倡导的开发思路。我们需要尽可能少的使用 useState，例如，这里借助了 Suspense 减少了 loading 状态的维护。

另外一个事情，是我们要特别特别注意观察子组件 `Message` 的实现。


首先因为我们初始化时，并没有给状态 `promise` 赋予的默认值。

```jsx
// 记住这个初始值
const [promise, update] = useState()
```

之后，我们就将状态 promise 传给了子组件 `Message`

```javascript
<Suspense fallback={<Skeleton />}>
  <Message promise={promise} />
</Suspense>
```

然后在 Message 组件的内部实现中，因为我们直接把 promise 传给了 `use`，那么此时直接执行肯定会报错

```tsx
// error 
const {value} = use(props.promise)
```

要注意的是，我们刚才说，使用 `Suspense` 会捕获子组件的异常，但是不是捕获所有异常，它只能识别 promise 的异常。因此，这里的报错会直接影响到整个页面。

所以，为了处理好这种异常，我在内部实现代码逻辑中，使用了 `if` 判断该条件，然后执行了一次 `return`。我试图让 `use(null)` 得不到执行的时机。

```jsx
function Message(props) {
  if (!props.promise) {
    return ...
  }

  const {value} = use(props.promise)
  return ...
}
```

还有一种方式是，我们可以给状态 promise 一个默认的，自带 resolve 值的 Promise 对象作为初始值，这样可以在子组件中避免这个异常判断。

```ts
// 合理默认值
const [promise, update] = useState(Promise.resolve({value: ''}))
```

演示效果如下图所示

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs message.tsx -c
!from ./demo02/message.tsx
```

```tsx !!tabs api.ts -c
!from ./demo02/api.ts
```
</CodeTabs>

这种写法有一个瑕疵，就是在初始化时，也不可避免的显示了 Skeleton 组件，实际上是不需要的。因此具体采用哪种写法，要依据实践中的需求而定。
