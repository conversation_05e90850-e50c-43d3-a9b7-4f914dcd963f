class MinStack {
  stack: Array<number>;
  min: Array<number>;
  constructor() {
    this.stack = []
    this.min = []
  }

  push(val: number): void {
    this.stack.push(val)
    this.min.push(Math.min(val, this.getMin() ?? Infinity))
  }

  pop(): void {
    this.stack.pop()
    this.min.pop()
  }

  top(): number {
    return this.stack[this.stack.length - 1]
  }

  getMin(): number {
    return this.min[this.min.length - 1]
  }
}