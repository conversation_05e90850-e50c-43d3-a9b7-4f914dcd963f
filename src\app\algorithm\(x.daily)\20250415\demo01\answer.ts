class TreeNode {
  val: number
  left: TreeNode | null
  right: TreeNode | null
  constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
    this.val = (val===undefined ? 0 : val)
    this.left = (left===undefined ? null : left)
    this.right = (right===undefined ? null : right)
 }
}

function maxDepth(root: TreeNode | null): number {
  if (root === null) {
      return 0
  }
  return Math.max(maxDepth(root.left), maxDepth(root.right)) + 1
};

export function isBalanced(root: TreeNode | null): boolean {
  if (root === null) {
    return true
  }
  const leftDepth = maxDepth(root.left)
  const rightDepth = maxDepth(root.right)
  
  return Math.abs(leftDepth - rightDepth) <= 1 && isBalanced(root.left) && isBalanced(root.right)
}
