import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import Demo02 from './demo02/preview'

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs userinfo.tsx -c
!from ./demo02/userinfo.tsx
```

```tsx !!tabs api.ts -c
!from ./demo02/api.ts
```
</CodeTabs>

这一章我们要学习的是一个新增加载项到列表的案例。首先我们会通过接口获取到一条数据，然后将该数据渲染到列表中。


### 1、**使用旧的方案实现**

首先，先定义请求数据的 promise

```ts api.ts -c
!from ./demo01/api.ts
```

然后需要定义一个状态用于存储列表。

```ts
// Array<UserInfo>
const [list, updateList] = useState([])
```

由于每一项在请求时，都需要显示一个 Loading 状态，此时我们可以使用一个巧妙的方式来解决这个问题。那就是暂时往 list 中新增一条 `type: loading` 的数据。在遍历的时候判断出该数据渲染成 `Skeleton` 组件。

因此，我们单独声明一个列表组件 List，该组件接收 `list` 作为参数

```ts
function List(props) {
  const list = props.list
  return (
    <div>
      {list.map((item, index) => {
        if (item.type === 'loading') {
          return <Skeleton />
        }
        return <Userinfo index={index} username={item.id} message={item.value} />
      })}
    </div>
  )
}
```

当我们在发送请求时，先往 list 中新增一条 `type: loading` 的数据。此时我们利用 list 的特性与闭包的缓存特性，在接口请求成功之后再把请求过来的有效数据更新到 list 中即可。

```ts
useEffect(() => {
  updateList([...list, {type: 'loading'}])
  getMessage().then(res => {
    updateList([...list, res])
  })
}, []);
```

完整代码如下：

<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs list.tsx -c
!from ./demo01/list.tsx
```

```tsx !!tabs userinfo.tsx -c
!from ./demo01/userinfo.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>


### 2、**新的思路**

旧的思路在实现上非常巧妙。但是简洁度依然弱于新的实现方案。除此之外，旧的实现思路还有许多问题需要处理，例如初始化时请求了两次，我们要考虑接口防重的问题。以及当我们多次连续点击按钮时，会出现竞态问题而导致渲染结果出现混乱。

我们基于 use + Suspense 的思路来考虑新的方案。

首先，我们应该将数据存储在 promise 中，因此很自然就能想到，多个数据，那么我们应该需要维护多个 promise，因此，我们需要定义一个由 promise 组成的数组。

```ts
import {getMessage} from './api'
...
// !mark
const [promise, updatePromise] = useState(() => [getMessage()])
...
```

由于初始化时，我们需要自动请求一条数据，因此我们给该数组的初始值为 `[getMessage()]`

点击时，需要新增一个数据，那么其实就是新增一个 promise，所以代码也非常简单，就是如下所示

```ts
function __handler() {
  updatePromise([...promise, getMessage()])
}
```

处理好之后，我们只需要使用 map 遍历该数组即可。在遍历逻辑中，每一项都返回 Suspense 包裹的子组件。我们将 promise 传递给该子组件，并在子组件中使用 use 读取 promise 中的值。

最终的完整代码如下

<Demo02 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo02/index.tsx
```

```tsx !!tabs userinfo.tsx -c
!from ./demo02/userinfo.tsx
```

```tsx !!tabs api.ts -c
!from ./demo02/api.ts
```
</CodeTabs>

你可以通过快速点击新增按钮观察演示效果。

并且当我们多次连续点击新增时，不会出现接口竞态混乱的问题。

希望大家能够通过这个案例，进一步感受到新的开发思维的强大之处。