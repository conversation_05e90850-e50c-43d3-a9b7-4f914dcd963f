class SortedStack {
  stack: number[];
  tmp: number[];
  constructor() {
    this.stack = []
    this.tmp = []
  }

  push(val: number): void {
    while(this.stack.length && this.stack[this.stack.length - 1] < val) {
      this.tmp.push(this.stack.pop())
    }
    while(this.tmp.length && this.tmp[this.tmp.length - 1] > val) {
      this.stack.push(this.tmp.pop())
    }
    this.stack.push(val)
  }

  pop(): void {
    // 将tmp的倒回stack 返回栈顶元素
    while(this.tmp.length) {
      this.stack.push(this.tmp.pop())
    }
    if (this.stack.length) {
      this.stack.pop()
    }
  }

  peek(): number {
    while(this.tmp.length) {
      this.stack.push(this.tmp.pop())
    }
    if (this.stack.length) {
      return this.stack[this.stack.length - 1]
    }
    return -1
  }

  isEmpty(): boolean {
    return this.stack.length === 0 && this.tmp.length === 0
  }
}