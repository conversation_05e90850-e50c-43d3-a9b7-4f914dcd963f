import { useRef } from 'react'

import { maxDepth } from './answer'

export default function Demo01() {
  const input = useRef([14, 2, 11, 19, 6, 18, 8, 20, 11])
  const k = useRef(5)

  return (
    <div className=''>
      <div className='text-sm text-gray-600 my-2'>输入</div>
      <div className='bg-gray-100 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'>[{input.current.join(', ')}]</div>
      </div>

      <div className='bg-gray-100 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>k = </div>
        <div className='code font-bold'>{k.current}</div>
      </div>

      <div className='text-sm text-gray-600 my-2'>输出</div>
      <div className='bg-gray-100 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'></div>
      </div>
    </div>
  )
}