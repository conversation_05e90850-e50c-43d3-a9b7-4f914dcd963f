import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'


<Demo01 />
<CodeTabs>
```tsx !!tabs index.tsx -c
!from ./demo01/index.tsx
```

```tsx !!tabs tabs.tsx -c
!from ./demo01/tabs.tsx
```

```tsx !!tabs config.ts -c
!from ./demo01/config.ts
```

```tsx !!tabs account.tsx -c
!from ./demo01/account.tsx
```

```tsx !!tabs search.tsx -c
!from ./demo01/search.tsx
```

```tsx !!tabs list.tsx -c
!from ./demo01/list.tsx
```

```tsx !!tabs Input.tsx -c
!from ./demo01/Input.tsx
```

```tsx !!tabs api.ts -c
!from ./demo01/api.ts
```
</CodeTabs>


这个案例的实现就稍微有点难度了。我们需要满足如下条件

1. 1、初始化时，只渲染默认的当前列页，而不会渲染其他任何未被点击过的页面/组件。
2. 2、渲染过的页面，在切换离开之后，需要缓存下来，下一次切换回事来直接显示即可，效果类似于 keep-alive
3. 3、每个 tab 按钮，对应一个页面，按钮与页面之间应该是 1 对 1 的关系

首先，我们应该设计 tabs 的格式如下。

```ts config.ts -c
!from ./demo01/config.ts
```
由于是一对一的关系，因此每个项可以自由对应需求所需要的页面组件

> 这里需要注意的是，虽然有几个对应的组件名是一样的，但是他们最终的实例是不同的

接下来比较难的是，如何设计数据来确保同时满足上面的几个需求。

我的方案如下

首先，定义一个 map 对象，用于存储已经渲染过的页面。

```ts
const map = useRef(new Set([tabs[current]]))
```

每次点击时，将页面案例加进来，但是在这之前，需要判断是否已经存在

```ts -c
function __handler(index) {
  tabs[current].current = false
  tabs[index].current = true

  const item = tabs[index]
  if (!map.current.has(item)) {
    map.current.add(item)
  }

  switchToSelected(index)
}
```

最后遍历 map 中的数据将页面渲染出来即可。

这里需要注意的是，由于页面是 1 对 1 的关系，因此，每个页面的数据单独维护即可。

例如其中一个组件代码如下

```tsx account.tsx -c
!from ./demo01/account.tsx
```

这里我们还要考虑一个布局的问题。只能当前选中的页面显示出来。因此我们这里通过利用 `display` 的值来控制。传入一个 `selected` 来判断是否需要将其值从 `block` 改变为 `none`
