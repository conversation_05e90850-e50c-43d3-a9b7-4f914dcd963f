import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 最长公共子序列

<Link href='https://leetcode.cn/problems/longest-common-subsequence/'>原题地址：leetcode 1143</Link>

### 题目描述

给定两个字符串 `text1` 和 `text2`，返回这两个字符串的最长公共子序列的长度。如果不存在公共子序列，则返回 0。

一个字符串的子序列是指这样一个新的字符串：它是由原字符串在不改变字符的相对顺序的情况下删除某些字符（也可以不删除任何字符）后组成的新字符串。

例如，`"ace"` 是 `"abcde"` 的子序列，但 `"aec"` 不是 `"abcde"` 的子序列。

两个字符串的公共子序列是这两个字符串所共同拥有的子序列。

考察点：双循环、迭代

### 解题思路

针对这种明显需要二维表格的题目，在解题思路上，我们可以首先定义一个二维表格。

> 许多解题思路会强调动态规划，实际上也可以不用与动态规划强关联，我们这个阶段只关注如何在双循环的过程中，找到规律

两个字符串分别作为表格的行和列，并用索引标记他们，每个单元格的值，默认值为 0 

现有两个字符串，`acefadm`，`gfdm`，他们的索引分别为 `i` 和 `j`，对应的表格如下：

![](/images/algorithm/daily/20250418/1.jpg)

> 这里索引为 0 的位置，默认值为空字符串，是为了兼容传入的字符串为空的情况

然后我们从序列为 `1` 开始双循环遍历两个字符串。当我们发现两个字符串的索引位置字符相等时，我们就在表格中对应的单元格中填入 `1`

![](/images/algorithm/daily/20250418/2.jpg)

接下来就是关键的地方。由于我们要找的是最长公共子序列的数量，因此，当我们找到一个之后，那么之后程序运行的结果，最小值也应该为 `1`，表示在之前已经找到了一个，所以，此时 `1` 的值会传递到后续的单元格中去

如图所示，我用箭头表达了后续的传递方向

![](/images/algorithm/daily/20250418/3.jpg)

注意结合传递方向理解，哪些单元格是**后续的单元格**

因此，对于每个单元格，不仅要判断当前索引位置对应的字符串是否相等，还需要判断传递而来的值是多少，传递而来的方向有两个，上侧和左侧，由于我们的目的是为了得到最长子序列，所以需要取最大值

![](/images/algorithm/daily/20250418/4.jpg)

依次类推，直到再次找到一个相同的字符。此时，我们需要在传递值的基础之上，再增加 `1`

+ 判断出来当前索引位置对应的字符串已经相等
+ 判断两个方向传递过来的值，取最大值
+ 累加一个
+ 然后将累加之后的值，继续传递给后续的单元格

![](/images/algorithm/daily/20250418/5.jpg)

这样，遍历到最后，我们会把最长的公共子序列的长度，传递到最后一个单元格

![](/images/algorithm/daily/20250418/6.jpg)

此时，表格中右下角最后一个单元格的值，就是我们需要的答案


我们再用图例来分析一个更复杂的情况。字符串 `acefadm` 和 `eafdm`。如图所示，按照上面的思路，当我们遍历到图中所示的单元格时，发现，从两个方向传递过来的值，都是 `1`

这种情况表达的是，有多个序列起点，但他们不属于同一个公共子序列，此时我们依然取最大值

![](/images/algorithm/daily/20250418/7.jpg)


另外一种情况是，同一个字符多次出现。如下图所示，在横轴上，`a` 出现了两次，但是纵轴上，`a` 只出现了一次，此时，从逻辑上来分析，由于横轴上的 `a` 在之前已经出现过一次，但是我们也不应该在第二次出现时去累加，因为纵轴上的 `a` 只出现了一次，不应该将其累加到 2

![](/images/algorithm/daily/20250418/8.jpg)

因此，我们要重新思考此时这个单元格的值，应该怎么算。之前，我们算的是从两个方向传递过来的值，取最大值，然后累加一个。但是我们换个角度思考一下，上、左两个方向的值，实际上是**上左**的单元格传递而来，因此，我们只需要从原始值中去累加即可

所以，如果我们发现当前索引位置对应的字符串相等，那么当前单元格的值，应该等于**上左**的单元格的值，加上 `1`

```ts
const current = dp[i - 1][j - 1] + 1
```

![](/images/algorithm/daily/20250418/9.jpg)

然后继续遍历传递，直到遍历结束，最后的结果如下

![](/images/algorithm/daily/20250418/10.jpg)


### 代码实现

```ts
function longestCommonSubsequence(text1: string, text2: string): number {
  const m = text1.length, 
        n = text2.length;

  // 初始化二维表格 
  const dp = new Array(m + 1).fill(0).map(() => new Array(n + 1).fill(0));

  // 遍历两个字符串
  for (let i = 1; i <= m; i++) {
    const c1 = text1[i - 1];
    for (let j = 1; j <= n; j++) {
      const c2 = text2[j - 1];

      // 如果当前字符相等，则当前单元格的值等于**上左**的单元格的值，加上 `1`
      if (c1 === c2) {
        dp[i][j] = dp[i - 1][j - 1] + 1;
      } else {
        // 否则，取**上**和**左**的单元格的值，取最大值
        dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
      }
    }
  }
  return dp[m][n];
}
```

### 复杂度分析

+ 时间复杂度：$O(m * n)$
+ 空间复杂度：$O(m * n)$

### 总结

在许多解析中，会将本题与动态规划联系起来，不过由于我们还没有进一步归纳动态规划的基础知识，因此，我们在解析的过程中并没有提到动态规划的概念。在其他地方，会将这种表格称之为 **DP数组**，我们也可以称之为**状态表**，因为表格中存储的值，实际上是状态值。

在解题的过程中，我们要学会掌握这种规律，并将其应用到其他题目中。