'use client'

import { motion } from 'framer-motion'
import { variants } from './variants'

import Link from 'next/link'
import ParticleBackground from './ParticleBackground'
import { AlertCircle, Euro } from 'lucide-react'

interface CourseCardProps {
  title: string
  description: string
  originalPrice: number
  discountPrice: number
  badge?: string
  id: string
  url?: string
}

const CourseCard = ({ title, description, originalPrice, discountPrice, url = '', badge, id }: CourseCardProps) => {
  // 生成随机颜色
  const getRandomColor = () => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500',
      'bg-red-400', 'bg-teal-500', 'bg-orange-500'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const bgColor = getRandomColor();

  return (
    <Link href={url || `/${id}`}>
      <div
        className="border-2 border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
      >
        <div className="relative">
          <ParticleBackground color={bgColor}>
            <span className="text-black dark:text-white font-bold text-xl">{title}</span>
          </ParticleBackground>
        </div>
        <div className="p-4">
          <p className="text-gray-600 dark:text-gray-400 mb-2">{description}</p>
          <div className="flex items-center">
            <span className="text-red-500 dark:text-red-400 font-bold text-xl">¥{discountPrice}</span>
            <span className="text-gray-400 dark:text-gray-500 line-through ml-2 text-sm">¥{originalPrice}</span>
          </div>
        </div>
      </div>
    </Link>
  )
}


const hotCourses = [
  {
    id: "",
    url: 'https://xinyu.zone/column/1818097927437131776',
    title: "JavaScript 核心进阶",
    description: "只给你地道的前端进阶思维",
    originalPrice: 149,
    discountPrice: 89,
    badge: "Hot"
  },
  {
    id: "r19base",
    title: "React 19 . 基础",
    description: "React19 为开发者带来了新的开发理念",
    originalPrice: 100,
    discountPrice: 40,
    badge: "Hot"
  },
  {
    id: "r19plus",
    title: "React 19 架构 . 尊享",
    description: "掌握一套高阶代码架构",
    originalPrice: 1000,
    discountPrice: 648,
    badge: "Hot"
  }
]

export default function HotestCurse() {
  return (
    <section className="py-4">
      <div className="mx-4 md:mx-auto max-w-6xl py-4 md:py-8 px-4 md:px-8 border-2 border-gray-200 dark:border-gray-700 rounded-lg border-dashed">
        <div className="flex items-center mb-4 md:mb-8">
          <Euro className="mr-2" size={20} />
          <h2 className="text-sm md:text-lg font-bold">
            <span className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-transparent bg-clip-text">
              销量最好的课程
            </span>
          </h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          {hotCourses.map((course, index) => (
            <CourseCard
              key={index}
              id={course.id}
              url={course.url}
              title={course.title}
              description={course.description}
              originalPrice={course.originalPrice}
              discountPrice={course.discountPrice}
              badge={course.badge}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
