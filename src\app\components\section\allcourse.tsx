'use client'

import Link from 'next/link'
import React from 'react'
import { Euro } from 'lucide-react'

interface CourseCard {
  id: string
  title: string
  description: string
  currentPrice: number
  originalPrice: number
  tags: string[]
  updatedAt: string
}

const AllCourses: React.FC = () => {
  // 模拟课程数据
  const courses: CourseCard[] = [
    {
      id: 'r19base',
      title: 'React19 . 基础版',
      description: '新的 React 开发思维',
      currentPrice: 40,
      originalPrice: 99,
      tags: ['热门', '最新'],
      updatedAt: '2025-01-20'
    },
    {
      id: 'r19plus',
      title: 'React19 . 尊享版',
      description: '掌握一套成熟的代码架构',
      currentPrice: 400,
      originalPrice: 1000,
      tags: ['热门'],
      updatedAt: '2025-02-18'
    },
    {
      id: 'zustand',
      title: 'Zustand',
      description: 'React状态管理库',
      currentPrice: 9.9,
      originalPrice: 200,
      tags: ['普通'],
      updatedAt: '2025-01-15'
    },
    {
      id: 'reactzm',
      title: 'React 知命境',
      description: '最好的 React 入门方式',
      currentPrice: 39,
      originalPrice: 99,
      tags: ['促销', '热门'],
      updatedAt: '2025-01-12'
    },
    {
      id: 'supercss',
      title: '超级 CSS',
      description: 'CSS 在实践中的运用',
      currentPrice: 200,
      originalPrice: 1000,
      tags: ['活动'],
      updatedAt: '2025-01-10'
    },
    {
      id: 'r19lightning',
      title: 'React 19 速成法',
      description: '短期内快速学会 React 19',
      currentPrice: 200,
      originalPrice: 1000,
      tags: ['活动'],
      updatedAt: '2025-01-10'
    }
  ]
  return (
    <div className='py-4'>
      <div className="mx-4 md:mx-auto max-w-6xl py-4 md:py-8 px-4 md:px-8 border-2 border-gray-200 dark:border-gray-700 rounded-lg border-dashed">
        <div className="flex items-center mb-4 md:mb-8">
          <Euro className="mr-2" size={20} />
          <h2 className="text-sm md:text-lg font-bold">
            <span className="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-transparent bg-clip-text">
              全部课程
            </span>
          </h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          {courses.map(course => (
            <Link href={`/${course.id}`} key={course.id} className="rounded-lg shadow-md overflow-hidden border-2 border-gray-200 dark:border-gray-700">
              <div className="relative h-32 flex items-center justify-center border-b-2 border-gray-200 dark:border-gray-700">
                {/* 课程标题装饰文字 */}
                <div className="text-2xl font-bold text-gray-800 dark:text-gray-200 z-10">
                  {course.title}
                </div>
              </div>

              <div className="p-4">
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">{course.description}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-xl font-bold text-red-500 dark:text-red-400">
                      ¥{course.currentPrice}
                    </span>
                    <span className="text-sm text-gray-400 dark:text-gray-500 line-through">
                      ¥{course.originalPrice}
                    </span>
                  </div>
                  <span className="text-xs text-gray-400 dark:text-gray-500">
                    更新于 {course.updatedAt}
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}

export default AllCourses
