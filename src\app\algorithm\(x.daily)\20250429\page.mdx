import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 练习题：颜色值转换

<Link href="https://www.nowcoder.com/practice/80b08802a833419f9c4ccc6e042c1cca?tpId=2&&tqId=10860&rp=1&ru=/activity/oj&qru=/ta/front-end/question-ranking">原题：颜色字符串转换</Link>

题目描述：

给定一个颜色值，将其转换为十六进制颜色值。例如 `rgb(255, 255, 255)` 转为 `#ffffff`

+ rgb 中每个 `,` 后面的空格数量不固定
+ 十六进制表达式使用六位小写字母
+ 如果输入不符合 rgb 格式，返回原始输入

---

**解题思路**

...


