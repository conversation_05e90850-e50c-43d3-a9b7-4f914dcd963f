export function maximumSubarraySum(nums: number[], k: number): number {
  let right = 0, 
      left = 0, 
      obj = {} as any, // 数组项的值为 key, 值 为 boolean
      ans = 0, // 存储最大值
      res = 0 // 存储当前窗口的和

  while (right < nums.length) {
    // 如果数组项在obj中存在  移动 left 指针，并将对应的值设置为false，直到没有重复项
    while (obj[nums[right]]) {
      obj[nums[left]] = false
      res -= nums[left++]
    }
    
    // 将当前值存入 obj 中，并将其值设置为 true，表示该值已经出现过一次
    obj[nums[right]] = true
    res += nums[right++]

    // 如果有k个了 取大值，计算好最大值之后，移动一次 left 指针，让窗口继续往右边滑动
    if (right - left === k) {
      ans = Math.max(ans, res)
      obj[nums[left]] = false
      res -= nums[left++]
    }
  }
  return ans
};