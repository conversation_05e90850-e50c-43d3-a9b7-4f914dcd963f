:root {
  --vp-sider-width: 246px;
  --vp-max-width: 1440px;
}

#vp-content {
  flex: 1;
  width: 100%;
}

aside#vp-aside {
  display: none;
}

aside#cp-aside {
  display: none;
}

#cp-content {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* tw md:xxx */
@media (min-width: 768px) {
  aside#vp-aside {
    display: flex;
    width: var(--vp-sider-width);
  }
  #vp-content {
    padding-left: var(--vp-sider-width);
  }
  #cp-content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* tw lg:xxx */
@media (min-width: 1024px) {
  aside#cp-aside {
    display: flex;
    max-width: var(--vp-sider-width);
  }
  #cp-content {
    padding-left: 5rem;
    padding-right: 5rem;
  }
}

/* tw 2xl:xxx */
@media (min-width: 1536px) {
  aside#vp-aside {
    width: calc((100% - var(--vp-max-width)) / 2 + var(--vp-sider-width));
    padding-left: calc((100% - var(--vp-max-width)) / 2);
    min-width: var(--vp-sider-width);
  }
  
  #vp-content {
    padding-right: calc((100vw - var(--vp-max-width)) / 2);
    padding-left: calc((100vw - var(--vp-max-width)) / 2 + var(--vp-sider-width));
  }

  #cp-content {
    padding-left: 6rem;
    padding-right: 6rem;
  }
}