import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'
import Demo01 from './demo01/preview'

### 栈

**栈**是一种**先进后出、后进先出**的线性数据结构。

![](/images/algorithm/struct/16.stack/1.png)

学习栈的相关知识，我们首先需要掌握栈容器的特性，其中包括

+ 栈是一个线性容器，他的一端是开口的，另外一端是封闭的。因此只能从开口的一端进出元素。
+ 元素从开口的一端进入叫做**入栈**，元素从开口的一端出来叫做**出栈**。
+ 元素出入的规则是**先进后出、后进先出**。
+ 一个栈包含两个重要的概念：栈顶和栈底，如上图所示
+ 栈结构是线性的、有序的，因此我们可以使用数组或链表来实现栈结构

从语法上来说，JavaScript 的数组本身就已经支持了栈的操作方式，因此，在实际开发中，我们不需要额外实现栈结构，直接使用数组即可。这也是最常规的的使用方式。

```ts
// 定义一个栈结构
const stack = [1, 2, 3]

// 入栈
stack.push(4)

// 出栈
const item = stack.pop()

// 查看栈顶元素
const top = stack[stack.length - 1]

// 查看栈的长度
const length = stack.length
```


> 不过，在把数组当成栈结构来使用时，我们需要自我约束，不要使用数组方法的其他操作方式，比如 `shift`、`unshift`、`splice` 等，这些操作会破坏栈的结构。


### 使用链表实现栈

使用链表来实现栈结构，并不是最优的方案，通常我们使用链表来实现栈结构，是为了加强对于栈操作的理解，特别是在元素出栈与入栈时，栈顶指针的移动方式。在真实的题目实现和应用场景中，更多的是直接使用数组来表达一个栈结构。

要结合栈的特性来实现一个链表栈，首先我们要有两个基础的栈指针：

+ `bottom`：栈底指针，指向栈底元素
+ `top`：栈顶指针，指向栈顶元素


然后，链表栈的实现需要包含以下几个方法：

+ `push`：入栈
+ `pop`：出栈
+ `peek`：查看栈顶元素
+ `size`：查看栈的长度
+ `isEmpty`：查看栈是否为空

入栈与出栈的过程，实际上栈顶指针移动的过程，因此，我们还需要一个变量来记录栈的长度，以备不时之需。

完整的代码如下

```ts Stack.ts -c
class Stack {
  constructor() {
    this.bottom = null
    this.top = null
    this.length = 0
  }

  push(item) {
    // 定义一个双向链表节点
    const node = {
      value: item,
      next: null,
      prev: null
    }

    // 如果栈为空，则将栈底和栈顶指针都指向新节点
    if (this.length === 0) {
      this.bottom = node
      this.top = node
    } else {
      // 如果栈不为空，则将新节点插入到栈顶
      node.prev = this.top
      this.top.next = node
      this.top = node
    }

    // 栈的长度加1
    this.length++
  }

  pop() {
    // 如果栈为空，则返回null
    if (this.length === 0) {
      return null
    }

    // 获取栈顶元素
    const node = this.top
    this.top = this.top.prev
    this.top.next = null
    this.length--
    return node
  }
  
  peek() {
    // 如果栈为空，则返回null
    if (this.length === 0) {
      return null
    }

    // 返回栈顶元素
    return this.top
  }

  size() {
    return this.length
  } 

  isEmpty() {
    return this.length === 0
  }
}
```


### 练习题：进制转化

题目描述：

给定一个十进制数，将其转换为指定进制的数。

---

**解题思路**

这里我们以目标结果为二进制为例来分析，其他进制可以类比。

我们知道，二进制是一堆由 0 和 1 组合而成的数字。它的特点就是 「逢 2 进 1」。那么如果需要要将 10 进制 的数字，转化为二进制的数字，应该怎么做呢？

在我上大学时的教科书上提供了一种方法，叫做「相除取余数」。

什么意思呢？举一个例子，我们要将数字 11 转化为二进制。那么就依次有

```ts
11 / 2 = 5 余 1  
// 使用计算结果的整数 5，继续下一次计算
5 / 2 = 2 余 1
// 使用计算结果的整数 2，继续下一次计算
2 / 2 = 1 余 0
// 使用计算结果的整数 1，继续下一次计算
1 / 2 = 0 余 1
```


这里，我们得到了每一次计算的余数，把所有的余数拼接起来，就是最终的二进制结果，为 1011 。再换一个数字 20，用这个逻辑继续推演一下

```ts
20 / 2 = 10 余 0
10 / 2 = 5 余 0 
5 / 2 = 2 余 1
2 / 2 = 1 余 0
1 / 2 = 0 余 1
```

那么 20 转化为二进制的结果，就是 `10100`

我们使用代码来实现这个推演过程，只需要在每一次计算时，将余数保存压入栈中，最后将所有栈中的数字拼接起来即可得到二进制结果。

```ts
function decimalToBinary(number) {
  // 声明一个栈容器
  const stack = []
  let result = ''

  while(number > 0) {
    // 将每一次计算的余数放入栈容器中
    stack.push(number % 2)

    // 计算下一次参与运算的结果
    number = Math.floor(number / 2)
  }

  // 拼接栈中的所有余数，得到二进制结果
  while(stack.length > 0) {
    result += stack.pop()
  }

  return result
}


console.log(decimalToBinary(11))   // 1011 
console.log(decimalToBinary(20))   // 10100
console.log(decimalToBinary(25))   // 11001
```

使用同样的方式，我们可以封装一个通用方法，用于将 10 进制转化为 2/8/16 进制的数字。具体代码如下：

```ts
/***
 * @desc 通用，10进制转其他进制
 * @param {number} number 10进制数字
 * @param {number} bs 2进制，8进制，16进制
 * @return {string} 转换结果 
 **/
function converter(number, bs) {
  const stack = []
  const digits = '0123456789ABCDEF'
  let result = ''

  while(number > 0) {
    stack.push(number % bs)
    number = Math.floor(number / bs)
  }

  while (stack.length > 0) {
    result += digits[stack.pop()]
  }

  return result
}

console.log(converter(11, 2)) // 1011
console.log(converter(20, 2)) // 10100
console.log(converter(25, 2)) // 11001
console.log(converter(25, 8)) // 31
console.log(converter(1231, 16))  // 4CF
```


### 练习题：颜色值转换

<Link href="https://www.nowcoder.com/practice/80b08802a833419f9c4ccc6e042c1cca?tpId=2&&tqId=10860&rp=1&ru=/activity/oj&qru=/ta/front-end/question-ranking">原题：颜色字符串转换</Link>

题目描述：

给定一个颜色值，将其转换为十六进制颜色值。例如 `rgb(255, 255, 255)` 转为 `#ffffff`

+ rgb 中每个 `,` 后面的空格数量不固定
+ 十六进制表达式使用六位小写字母
+ 如果输入不符合 rgb 格式，返回原始输入

<Link href="/algorithm/20250429">点击查看解题思路</Link>


