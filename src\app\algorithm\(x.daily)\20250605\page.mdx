import CodeTabs from 'components/codehike/code-tabs'
import Demo01 from './demo01/preview'
import { Link } from 'components/ui/link'

### 练习题：最近的请求次数

<Link href="https://leetcode.cn/problems/number-of-recent-calls/">原题：最近的请求次数</Link>

题目描述：

写一个 `RecentCounter` 类来计算特定时间范围内最近的请求。

请实现 `RecentCounter` 类：
+ `RecentCounter()` 初始化计数器，请求数为 0
+ `ping(int t)` 在时间 t 添加一个新请求，并返回过去 3000 毫秒内发生的所有请求数（包括新请求）。确切地说，返回在 `[t-3000, t]` 内发生的请求数。

保证每次对 `ping` 的调用都使用比之前更大的 t 值。


### 解题思路

这个题比较麻烦的地方在于如何理解题意。

此处使用 `ping` 方法来表示添加一个新请求，并在 `ping` 中传入一个表达时间戳的参数 `t`，来代表请求发生的时刻。返回过去 3000 毫秒内发生的所有请求数（包括新请求），就是返回在 `[t-3000, t]` 内发生的请求数。

因此，我们只需要使用一个队列来存储请求的时间戳，然后每次调用 `ping` 方法时，将当前时间戳加入队列，并返回队列中时间戳大于等于当前时间戳减去 3000 的请求数即可。

具体实现如下：

```ts RecentCounter.ts -c
class RecentCounter {
  queue: number[];
  constructor() {
    this.queue = [];
  }
  ping(t: number): number {
    this.queue.push(t);
    while (this.queue[0] < t - 3000) {
      this.queue.shift();
    }
    return this.queue.length;
  }
}
```