'use client'

import dynamic from 'next/dynamic'
import Preview, { FallbackRender } from 'components/preview'
import { ErrorBoundary } from 'react-error-boundary'

const Case = dynamic(() => import('./index'), {
  ssr: false,
})

export default function Previewer() {
  return (
    <Preview>
      <ErrorBoundary fallbackRender={FallbackRender}>
        <Case />
      </ErrorBoundary>
    </Preview>
  )
}
