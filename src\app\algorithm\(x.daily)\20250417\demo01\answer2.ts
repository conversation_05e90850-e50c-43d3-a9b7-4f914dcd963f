/**
 * @param {TreeNode} p
 * @param {TreeNode} q
 * @return {boolean}
 */
const isSameTree = function (p, q) {
  const stack1 = [p], stack2 = [q]
  const isSame = true
  while (stack1.length && stack2.length) {
      const n1 = stack1.pop()
      const n2 = stack2.pop()
      if (!n1 && !n2) continue //两个节点都是 null，说明结构相同，continue 不进行后续 push 子节点操作
      if (!n1 || !n2) { return false } //两个节点有一个是null, 结构不同
      if (n1.val !== n2.val) { return false } //两个节点值不一样, 不是相同的树

      stack1.push(n1.left)
      stack2.push(n2.left)
      stack1.push(n1.right)
      stack2.push(n2.right)
  }
  return stack1.length === 0 && stack2.length === 0 // 判断stack中没有可以继续pop的元素，两树总节点数相同
};