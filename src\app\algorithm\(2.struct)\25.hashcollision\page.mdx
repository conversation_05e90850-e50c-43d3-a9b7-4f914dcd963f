import CodeTabs from 'components/codehike/code-tabs'
import { Link } from 'components/ui/link'

## Table of contents

## 哈希碰撞

在上一章中，我们讨论了哈希表的直接定址法，但是这种哈希函数的设计理论，仅仅适合关键字连续分布的情况，如果关键字不连续，则会造成空间浪费。

例如，一个班级在大一的时候，有 100 个学生，学号是 1001 到 1100。但是随着后续的发展变化，会经历分班、转专业、休学、退学等情况，导致**学号空缺**比较多。此时，如果我们依然使用直接定址法，那么就会造成空间的大量浪费。

因此，我们期望设计一个哈希函数，能够在**不连续**的学号中，将学号尽可能的映射到有限数组的连续空间中。

![](/images/algorithm/struct/25.hashcollision/1.png)


那么针对这种情况，一个比较常用的哈希函数是：**除留余数法**，他的核心就是用一个关键字都整数，除以一个整数，用得到的余数去映射数组的下标

因此，这里我们以数组的长度作为除数，用关键字对数组长度取余，得到的结果就是数组的下标。

```ts title="除留余数法"
function hash(key: number, length: number) {
  return key % length;
}
```

由于我们这里的关键字学号是四位数，数组长度为 8，因此，我们可以针对实际情况对哈希函数进行调整，例如：

```ts title="除留余数法"
function hash(key: number) {
  return (key - 1000) % 8;
}
```

代入这个哈希函数，我们可以得到如下图所示的分布结果

![](/images/algorithm/struct/25.hashcollision/2.png)

我们可以看到，由于数组的空间有限，但是学生的个数比较多，因此，必然会有多个学生的学号映射到同一个数组下标中， 例如上图中的 1001 和 1033 都映射到了下标为 1 的位置，这就是**哈希碰撞**。


## 解决哈希碰撞的问题

很明显，哈希碰撞是一个无法正常使用的问题。因此，我们需要在此基础之上，进一步改变数据的存储方式，来解决冲突问题。

在哈希碰撞的情况之下，我们需要区分两种情况，

+ 一种是输入量大于数组的存储空间
+ 一种是输入量小于等于数组的存储空间「日常中几乎不会出现这种情况」

我们上面的图例演示了当输入量大于数组的存储空间时的情况，此时，同一个数组下标可能会映射到多个输入值。我们可以使用**拉链法**来解决冲突，也就是说，多个映射到同一个下标地址的输入，通过链表的形式存储在同一个数组下标的位置中

在这个基础之上，为了回顾之前学过的数据结构，我们这里使用环形链表来存储映射同一个下标的多个数据。

首先定义一个链表节点的格式，每一个节点上存储一个键值对，与链表指针

```ts 链表节点
type HNode<V> = {
  // 由于案例单一，因此这里我们将 key 的类型强制限定为 number
  key: number,
  value: V,
  next: HNode<V> | null
}
```

这里之所以需要使用环形链表，是因为在插入新的链表节点时，我们希望直接插入到链表尾部，但是单向链表要拿到最后一个节点的引用需要依次向后查询，这非常不方便，而我们使用环形链表可以通过指向尾部节点的方式，快速拿到尾部节点和头部节点，从而简化查询这个过程

> 除此之外，使用环形链表更多的用意是基于学习的目的，如果在逻辑中，本身就需要一个查询过程，例如在插入节点时判断链表上是否存在重复节点，就需要遍历链表，那么此时就可以使用单链表在共用这个逻辑去找到尾节点

然后，我们定一个头部指针指向链表的尾部节点，存储在数组中

```ts 头部指针
type Queue<V> = {
  length: number,
  last: HNode<V> | null
}
```

由于每一个数组节点都有可能存储一个链表，因此，我们可以把每一个数组节点看成是一个存储桶 `bucket`，因此，在定义 `HashMap` 类时，我们可以先简单写这样一个代码出来，把基础属性和需要的 API 先标记好

```ts 简版
export default class HashMap<V> {
  private buckets: Queue<V>[];
  private size: number;

  constructor(size = 32) {
    this.buckets = new Array(size);
    this.size = size;
  }

  // 哈希函数
  hash(key: number) {
    return (key - 1000) % this.size;
  }

  // 插入键值对
  set(key: number, value: V) {
    ...
  }

  // 获取值
  get(key: number) {
    ...
  }

  // 删除键值对
  delete(key: number) {
    ...
  }

  // 检查是否包含键
  has(key: number) {
    ...
  }

  // 获取所有键
  keys() {
    ...
  }

  // 获取所有值
  values() {
    ...
  }

  // 清空哈希表
  clear() {
    this.buckets = new Array(this.size);
  }
}
```

哈希函数我们使用除留余数法已经写好。接下来要考虑把节点存入数组中的情况。构建完成之后，我们期望得到的链表结构如下所示

![](/images/algorithm/struct/25.hashcollision/8.png)

第一种情况，初始化时，对应数组位置里面什么东西都没有，此时，我们需要创建 queue 对象，并 `queue.last` 指向新节点

![](/images/algorithm/struct/25.hashcollision/3.png)

第二种情况，链表节点全部被删除，只剩下 `queue` 节点，此时我们只需要创建新的节点即可

![](/images/algorithm/struct/25.hashcollision/4.png)

最终得到一个只有一个节点的链表，首尾节点都是自己

![](/images/algorithm/struct/25.hashcollision/5.png)

第三种情况，往链表尾节点之后添加新的节点。这里需要注意的是，`queue.last` 指向的是尾节点，因此指针的变化要小心理解错


![](/images/algorithm/struct/25.hashcollision/6.png)

再新增一个节点

![](/images/algorithm/struct/25.hashcollision/7.png)

再新增一个节点

![](/images/algorithm/struct/25.hashcollision/8.png)

环形链表往尾部新增节点理解起来比较困难，因此我这里多绘制了几张图，大家可以对比图例仔细揣摩。理解了新增节点情况与过程，我们的代码就很好写出来了

```ts
// 插入键值对
set(key: number, value: V) {
  const index = this.hash(key);
  const bucket = this.buckets[index]

  const node: HNode<V> = {
    key: key,
    value: value,
    next: null
  }
  
  // 如果桶是空的，初始化一个链表
  if (!bucket) {
    node.next = node
    this.buckets[index] = {
      length: 1,
      last: node
    };
    return
  }

  const last = bucket.last

  if (!last) {
    node.next = node
    bucket.last = node
    return
  }

  const first = last.next

  // 检查是否已存在相同的key
  let current = last
  while(true) {
    if (current.key === key) {
      current.value = value
      break
    }
    current = current.next!
    if (current === bucket.last) {
      break
    }
  }

  // add the node to last
  last.next = node
  node.next = first
  bucket.last = node
  bucket.length += 1
}
```

完整理解了如何插入链表节点，那么获取和删除就比较简单了。我们直接通过完整代码学习即可


<CodeTabs>
```ts !!tabs HashMap.ts -c
!from ./demo01/hashMap.ts
```
</CodeTabs>

> 在个别少数情况下，会有输入量关键字不连续，但是总体数量小于等于存储空间的。这种情况下我们可以使用**开放定址法**来解决冲突。这个方式的思路是遇到冲突则在数组中寻找下一个下标位置放下输入值。因为数组空间大于等于输入量，因此我们总能把所有的数据直接放到数组中去