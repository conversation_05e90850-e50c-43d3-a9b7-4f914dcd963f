这篇文章文字内容不会很多，但是是整个专栏最重要的部分之一。

本专栏会围绕如下三个层次逐渐为大家由浅入深分析 React19 的学习，他们确保了我们的学习方向的正确性与难度的循序渐进。

### *0*、**初级版：优化异步开发体验**

React 19 引入了好几个新的 hook 用于优化异步开发体验。

```tsx
use
useActionState
useFormStatus
useOptimistic
```

他们的主要目的是试图在项目开发中，引导开发者削弱 useEffect 的使用。因此我们在实践案例中，需要谨记这个核心思想，不至于在开发中偏离轨迹，而忽视了新 hook 的强大能力。

> 注：并非完全不用 useEffect

如果你只是专注于项目开发，学习到这里基本上已经可以了。因为大多数项目涉及不到更高的理解层次。大多数 React 开发者也很难通过自学有更强的理解。《React 19 全解》基础版也主要围绕这个目标来展开分享。

### *1*、**进阶版：强化并发模式**

**只有少部分顶级前端开发需要关注并发模式。** 他的理解难度并不低。充分消化并发模式并运用于实践能够让一个前端程序员完成蜕变，你可以很容易突破前端开发的瓶颈。

在 React 中，并发模式并非是一个新概念。在 React Fiber 架构提出之初，我们就陆陆续续的听说了它。

但是经过好几个大版本的迭代，并发模式在开发者中的感受并不深刻。甚至由于之前设计的几个并发模式的 API 不好用，导致很多开发者也对 React 的并发模式并不感兴趣。

React 19 从源码层面简化和改进了并发模式，摈弃了之前的同步模式。除此之外，React 19 还对并发模式的 API 进行了调整和优化，例如重新设计了 Suspense，它支持更好的代码逻辑拆分，我们可以在普通的页面开发中使用到它。

在后续的章节中，我们会单独学习并发模式所涉及到的 API。

### *2*、**顶级版：深入理解 React Compiler**

毫无疑问，React Compiler 的使用一定是简单的，但是对于其原理的透彻理解，就需要一点底子了。很显然，这**必定会称为面试中的常客**。因此，如果你希望在面试中有一个更好的表现，对其实现原理如数家珍会为你**上大分**。

当然，如果你并不需要在面试中通过表达自己对 Compiler 的理解来加分，那么这一部分也是可以不需要学习的。

正是由于他的必要性不是很高，我才把这一部分内容放到专栏的最后一个部分。理解它在项目开发中的实用价值并不高。

透彻理解 React Compiler 的原理，需要对现有 React 的底层原理有一个大概的了解，这样我们才能形成一个完整的知识体系闭环。学习之前，做好这个心理准备和知识铺垫即可。

> 在 **React 知命境** 中，我有几篇高质量的文章介绍了 React 的底层原理，读者朋友们可以去看看。
