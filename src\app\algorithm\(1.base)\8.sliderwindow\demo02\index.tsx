import { useEffect, useRef, useState } from 'react'
import Button from '@/components/ui/button'
import Number from './number'

import { maximumSubarraySum } from './max'
import { maxSum } from './max_a'

export default function Demo01() {
  const arr = useRef([1, 2, 3, 4, 5])
  const [current, setCurrent] = useState(0)

  const input = useRef([4, 2, 1, 7, 8, 1, 2, 8, 1, 0])
  const k = useRef(4)

  const [res, setRes] = useState(0)

  useEffect(() => {
    // console.log(maximumSubarraySum([1, 5, 4, 2, 9, 9, 9], 3))
    // console.log(maximumSubarraySum([14, 2, 11, 19, 6, 18, 8, 20, 11], 6))
    // console.log(maximumSubarraySum([1, 1, 1, 7, 8, 9], 3))
    // console.log(maximumSubarraySum([14, 7, 7, 7, 12, 7], 2))
    console.log(maxSum([1, 2, 3, 4, 5], 3))
  }, [])


  return (
    <div className=''>
      <div className='text-sm text-gray-600 my-2'>输入</div>
      <div className='bg-gray-100 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'>[{input.current.join(', ')}]</div>
      </div>

      <div className='bg-gray-100 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>k = </div>
        <div className='code font-bold'>{k.current}</div>
      </div>

      <div className='text-sm text-gray-600 my-2'>输出</div>
      <div className='bg-gray-100 rounded-md p-4 my-2'>
        <div className='text-sm text-gray-500 mb-2'>arr = </div>
        <div className='code font-bold'>{maxSum(input.current, k.current)}</div>
      </div>
    </div>
  )
}