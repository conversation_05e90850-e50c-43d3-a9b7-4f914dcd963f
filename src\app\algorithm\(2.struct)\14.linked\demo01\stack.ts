

class Stack {
  // 栈的元素
  private items: any[] = []
  // 当前栈的长度
  private length: number = 0
  // 容量
  private capacity: number = 0
  // 栈顶
  private top: number = 0
  // 栈底
  private bottom: number = 0

  constructor(capacity: number) {
    this.capacity = capacity
  }

  push(item: any) {
    this.items.push(item)
    this.length++
    this.top++
  }

  pop() {
    this.items.pop()
    this.length--
    this.top--
  }
  
  peek() {
    return this.items[this.top]
  }

  isEmpty() {
    return this.length === 0
  } 
}