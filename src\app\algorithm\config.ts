import { svip } from '@/app/svip'

export const column_id = '1909601373037600768'
export const column_url = `https://xinyu.zone/column/${column_id}`
export const column_key = 'algorithm'
export const start_path = '/algorithm/1.over'
export const reactversion = 'React@19.0.0'

export const user_ids: string[] = [
  '1813145181655089152', // 这波能反杀
  '1825335843305291776', // 希柴植物
  '1876904897136553984', // SPHINX
  '1843293842095513600', // 睡觉
  '1826957365438586880', // 生灭
  '1843423073341317120', // 赵江江
  '1843467711817228288', // 开心就好
  '1843289529826713600', // 张宁
  '1823968445075886080', // vr . little
  '1876633553983500288', // 宇
].concat(svip)