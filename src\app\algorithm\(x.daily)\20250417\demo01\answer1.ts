class TreeNode {
  val: number
  left: TreeNode | null
  right: TreeNode | null
  constructor(val?: number, left?: TreeNode | null, right?: TreeNode | null) {
    this.val = (val===undefined ? 0 : val)
    this.left = (left===undefined ? null : left)
    this.right = (right===undefined ? null : right)
 }
}

const isSameTree = function(p, q) {
  const s = [[p, q]]

  while(s.length !== 0) {
    if (p === null && q === null) {
      return true
    }
    if (p === null || q === null) {
      return false
    }
    if (p.val !== q.val) {
      return false
    }

    p && (p.x = true)
    q && (q.x = true)

    const top = s[s.length - 1]
    p = top[0]
    q = top[1]
    
    if ((p.left && !p.left.x) || (q.left && !q.left.x)) {
      p = p.left
      q = q.left
      s.push([p, q])
      continue   
    }

    if ((p.right && !p.right.x) || (q.right && !q.right.x)) {
      p = p.right
      q = q.right
      s.push([p, q])
      continue;
    }
    s.pop()
  }

  return true
};