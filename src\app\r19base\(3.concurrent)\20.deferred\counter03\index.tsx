import { useState, useDeferredValue } from 'react'
import Expensive from './expensive'

export default function Index() {
  const [counter, setCounter] = useState(0)
  const deferred = useDeferredValue(counter, 0)

  function __clickHanler() {
    setCounter(counter + 1)
  }

  return (
    <div className='flex justify-between items-center'>
      <div>
        <div>counter: {counter}</div>
        <Expensive counter={deferred} />
      </div>
      <button className='button' onClick={__clickHanler}>counter++</button>
    </div>
  )
}